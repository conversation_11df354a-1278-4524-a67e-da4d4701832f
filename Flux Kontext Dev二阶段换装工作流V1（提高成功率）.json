{"id": "96c5bc7f-602a-499b-91b6-662e34a933d6", "revision": 0, "last_node_id": 267, "last_link_id": 386, "nodes": [{"id": 202, "type": "DualCLIPLoader", "pos": [2476.27587890625, 814.2345581054688], "size": [337.76861572265625, 130], "flags": {"pinned": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [319, 354]}], "properties": {"Node name for S&R": "DualCLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.38", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 203, "type": "VAELoader", "pos": [2392.010009765625, 1015.8859252929688], "size": [337.76861572265625, 58], "flags": {"pinned": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [324, 328, 334, 358]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.38", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 204, "type": "CLIPTextEncode", "pos": [3377.654296875, 986.7821655273438], "size": [385.7064514160156, 93], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 319}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 320}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [321, 335]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {"text": true}}, "widgets_values": ["Using this elegant style, create a portrait of a swan wearing a pearl tiara and lace collar, maintaining the same refined quality and soft color tones."], "color": "#232", "bgcolor": "#353"}, {"id": 205, "type": "ConditioningZeroOut", "pos": [3659.54150390625, 858.6183471679688], "size": [240, 26], "flags": {"collapsed": false}, "order": 27, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 321}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [341]}], "properties": {"Node name for S&R": "ConditioningZeroOut", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 206, "type": "SaveImage", "pos": [4873.939453125, 290.1796569824219], "size": [407.2988586425781, 299.3293762207031], "flags": {"pinned": true}, "order": 36, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 322}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["T8star-Aix"], "color": "#223", "bgcolor": "#335"}, {"id": 207, "type": "VAEDecode", "pos": [4022.74462890625, 600.2445678710938], "size": [190, 46], "flags": {"collapsed": false, "pinned": true}, "order": 34, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 323}, {"label": "vae", "name": "vae", "type": "VAE", "link": 324}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [322, 332, 362]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 208, "type": "SaveImage", "pos": [5593.63818359375, 754.6080932617188], "size": [315, 270], "flags": {"pinned": true}, "order": 50, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 325}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ComfyUI"], "color": "#223", "bgcolor": "#335"}, {"id": 209, "type": "Text Multiline", "pos": [2488.807861328125, 357.5431823730469], "size": [375.3995361328125, 206.6213836669922], "flags": {"pinned": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [366]}], "properties": {"Node name for S&R": "Text Multiline", "cnr_id": "was-node-suite-comfyui", "ver": "ea935d1044ae5a26efa54ebeb18fe9020af49a45", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["移除女人的衣服"], "color": "#223", "bgcolor": "#335"}, {"id": 210, "type": "ConditioningZeroOut", "pos": [4144.25927734375, 1596.02978515625], "size": [240, 26], "flags": {"collapsed": false}, "order": 28, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 326}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [350]}], "properties": {"Node name for S&R": "ConditioningZeroOut", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 211, "type": "VAEDecode", "pos": [5066.02734375, 1519.359619140625], "size": [190, 46], "flags": {"collapsed": false, "pinned": true}, "order": 46, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 327}, {"label": "vae", "name": "vae", "type": "VAE", "link": 328}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [338, 353, 384]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 216, "type": "ImageConcatMulti", "pos": [4451.111328125, 569.5211791992188], "size": [315, 150], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 331}, {"label": "image_2", "name": "image_2", "type": "IMAGE", "link": 332}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [361]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "f7eb33abc80a2aded1b46dff0dd14d07856a7d50", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2, "down", true, null], "color": "#223", "bgcolor": "#335"}, {"id": 217, "type": "VAEEncode", "pos": [3381.999267578125, 857.6554565429688], "size": [240, 50], "flags": {"collapsed": false}, "order": 25, "mode": 0, "inputs": [{"label": "pixels", "name": "pixels", "type": "IMAGE", "link": 333}, {"label": "vae", "name": "vae", "type": "VAE", "link": 334}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [336, 342]}], "properties": {"Node name for S&R": "VAEEncode", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 218, "type": "ReferenceLatent", "pos": [3409.921142578125, 728.6439819335938], "size": [211.60000610351562, 46], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 335}, {"label": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 336}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [337]}], "properties": {"Node name for S&R": "ReferenceLatent", "cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 229, "type": "Text Multiline", "pos": [3000.587890625, 1501.92431640625], "size": [375.3995361328125, 206.6213836669922], "flags": {"pinned": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [330]}], "properties": {"Node name for S&R": "Text Multiline", "cnr_id": "was-node-suite-comfyui", "ver": "ea935d1044ae5a26efa54ebeb18fe9020af49a45", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["让裸体的女人穿上图片中的衣服，且人物脸部，表情，动作保持不变"], "color": "#223", "bgcolor": "#335"}, {"id": 230, "type": "ReferenceLatent", "pos": [3885.989990234375, 1483.637939453125], "size": [211.60000610351562, 46], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 344}, {"label": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 345}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [364]}], "properties": {"Node name for S&R": "ReferenceLatent", "cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 232, "type": "ImagePadForOutpaint", "pos": [4139.25146484375, 2218.72314453125], "size": [270, 174], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 376}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [347, 363]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "ImagePadForOutpaint", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [200, 200, 200, 200, 40], "color": "#223", "bgcolor": "#335"}, {"id": 243, "type": "VAEEncode", "pos": [4609.34130859375, 1542.946533203125], "size": [240, 50], "flags": {"collapsed": false}, "order": 41, "mode": 0, "inputs": [{"label": "pixels", "name": "pixels", "type": "IMAGE", "link": 357}, {"label": "vae", "name": "vae", "type": "VAE", "link": 358}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [345]}], "properties": {"Node name for S&R": "VAEEncode", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 244, "type": "FluxKontextImageScale", "pos": [2963.603271484375, 861.8490600585938], "size": [314.1544189453125, 28.620174407958984], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 359}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [333]}], "properties": {"Node name for S&R": "FluxKontextImageScale", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 245, "type": "LoraLoaderModelOnly", "pos": [2785.21484375, 1169.59521484375], "size": [270, 82], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 360}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [329]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["clothes_remover_v0.safetensors", 1], "color": "#223", "bgcolor": "#335"}, {"id": 247, "type": "UnetLoaderGGUF", "pos": [2121.93505859375, 1434.637451171875], "size": [270, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [369, 373]}], "properties": {"Node name for S&R": "UnetLoaderGGUF", "cnr_id": "comfyui-gguf", "ver": "b3ec875a68d94b758914fd48d30571d953bb7a54", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["flux1-kontext-dev-Q8_0.gguf"], "color": "#223", "bgcolor": "#335"}, {"id": 248, "type": "ImageStitch", "pos": [3995.90966796875, 1720.977294921875], "size": [270, 150], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 362}, {"label": "image2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 363}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [343, 357, 365]}], "properties": {"Node name for S&R": "ImageStitch", "cnr_id": "comfy-core", "ver": "0.3.40", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["right", true, 0, "white"], "color": "#223", "bgcolor": "#335"}, {"id": 250, "type": "FluxKontextImageScale", "pos": [4673.87255859375, 1801.12890625], "size": [523.490234375, 137.1434783935547], "flags": {"collapsed": false}, "order": 42, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 365}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": []}], "properties": {"Node name for S&R": "FluxKontextImageScale", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 251, "type": "GoogleTranslateTextNode", "pos": [2933.392822265625, 402.2062683105469], "size": [400, 200], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 366}], "outputs": [{"label": "text", "name": "text", "type": "STRING", "links": [318, 320]}], "properties": {"Node name for S&R": "GoogleTranslateTextNode", "cnr_id": "comfyui_custom_nodes_alekpet", "ver": "a77f4034d9a038efd80d17eedcfcba50248a13a8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["zh-cn", "en", false, "Manual Trasnlate", ""], "color": "#223", "bgcolor": "#335"}, {"id": 222, "type": "FluxGuidance", "pos": [3702.************, 718.5477905273438], "size": [240, 58], "flags": {"collapsed": false}, "order": 32, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 337}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [340]}], "properties": {"Node name for S&R": "FluxGuidance", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [10], "color": "#223", "bgcolor": "#335"}, {"id": 254, "type": "<PERSON><PERSON><PERSON><PERSON>", "pos": [2489.************, 1298.010986328125], "size": [315.3089294433594, 178], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 369}], "outputs": [{"label": "model", "name": "model", "type": "MODEL", "links": [360]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>", "aux_id": "Zehong-Ma/ComfyUI-MagCache", "ver": "9a785013fecad3a56b0089b3369ec35ce378e81b", "widget_ue_connectable": {}}, "widgets_values": ["flux", 0.20000000000000004, 0.10000000000000002, 2], "color": "#322", "bgcolor": "#533", "shape": 1}, {"id": 249, "type": "FluxGuidance", "pos": [4362.12646484375, 1388.9449462890625], "size": [240, 58], "flags": {"collapsed": false}, "order": 44, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 364}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [349]}], "properties": {"Node name for S&R": "FluxGuidance", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [1], "color": "#223", "bgcolor": "#335"}, {"id": 255, "type": "easy showAnything", "pos": [3648.************, 1972.6990966796875], "size": [210, 88], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "shape": 7, "type": "*", "link": 375}], "outputs": [{"label": "output", "name": "output", "type": "*"}], "properties": {"Node name for S&R": "easy showAnything", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["Let the naked woman wear the clothes in the picture, and keep the characters' faces, expressions and movements unchanged"], "color": "#223", "bgcolor": "#335"}, {"id": 215, "type": "GoogleTranslateTextNode", "pos": [3074.************, 1948.6009521484375], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 330}], "outputs": [{"label": "text", "name": "text", "type": "STRING", "links": [355, 375]}], "properties": {"Node name for S&R": "GoogleTranslateTextNode", "cnr_id": "comfyui_custom_nodes_alekpet", "ver": "a77f4034d9a038efd80d17eedcfcba50248a13a8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["zh-cn", "en", false, "Manual Trasnlate", ""], "color": "#223", "bgcolor": "#335"}, {"id": 234, "type": "K<PERSON><PERSON><PERSON>", "pos": [4816.8779296875, 947.2260131835938], "size": [318.5348205566406, 486], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 374}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 349}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 350}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 351}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [327]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [95567542424, "fixed", 20, 1, "euler", "simple", 1], "color": "#223", "bgcolor": "#335"}, {"id": 225, "type": "K<PERSON><PERSON><PERSON>", "pos": [4431.8115234375, -139.7356414794922], "size": [329.6587829589844, 474.0001220703125], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 339}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 340}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 341}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 342}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [323, 351, 377]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [877255100689250, "fixed", 20, 1, "euler", "simple", 1], "color": "#223", "bgcolor": "#335"}, {"id": 258, "type": "SaveLatent", "pos": [4925.0009765625, -88.90827178955078], "size": [315, 58], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 377}], "outputs": [], "properties": {"Node name for S&R": "SaveLatent", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["latents/ComfyUI"], "color": "#223", "bgcolor": "#335"}, {"id": 259, "type": "VAELoader", "pos": [1919.3255615234375, 2647.4033203125], "size": [337.76861572265625, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [380]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.38", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 260, "type": "LoadLatent", "pos": [1936.2891845703125, 2537.1376953125], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [379]}], "properties": {"Node name for S&R": "LoadLatent", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [null], "color": "#223", "bgcolor": "#335"}, {"id": 261, "type": "SaveImage", "pos": [2637.462890625, 2585.202880859375], "size": [315, 58], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 378}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ComfyUI"], "color": "#223", "bgcolor": "#335"}, {"id": 262, "type": "VAEDecode", "pos": [2354.427734375, 2590.219970703125], "size": [190, 46], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 379}, {"label": "vae", "name": "vae", "type": "VAE", "link": 380}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [378]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 239, "type": "SaveImage", "pos": [5356.64453125, 1202.42431640625], "size": [862.7942504882812, 665.824462890625], "flags": {}, "order": 48, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 353}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ComfyUI", ""], "color": "#223", "bgcolor": "#335"}, {"id": 212, "type": "LoadImage", "pos": [2025.489990234375, -71.45658111572266], "size": [340, 350], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [331, 359, 381]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["3c3d8a7e1f7091c1df3e1329d45b4b9016ef565281bad44b5d924875fa8f7a22.jpg", "image", ""], "color": "#322", "bgcolor": "#533"}, {"id": 256, "type": "LoadImage", "pos": [3713.997802734375, 2282.251220703125], "size": [340, 350], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [376, 382]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["d65a5715120720235adcbc66428d201e4a764817b1ab43b5f7ed368d944bd3bb.webp", "image", ""], "color": "#322", "bgcolor": "#533"}, {"id": 263, "type": "ImageConcanate", "pos": [5628.9208984375, 2123.06884765625], "size": [315, 102], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 381}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 382}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [385]}], "properties": {"Node name for S&R": "ImageConcanate", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["down", true], "color": "#223", "bgcolor": "#335"}, {"id": 265, "type": "ImageConcanate", "pos": [5647.71923828125, 2312.4638671875], "size": [315, 102], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 385}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 384}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [386]}], "properties": {"Node name for S&R": "ImageConcanate", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["right", true], "color": "#223", "bgcolor": "#335"}, {"id": 267, "type": "Note", "pos": [4462.59423828125, 2223.675048828125], "size": [331.05810546875, 191.084716796875], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["加一点外补似乎会让衣服更好换，，"], "color": "#432", "bgcolor": "#653"}, {"id": 266, "type": "SaveImage", "pos": [6286.49169921875, 1962.4169921875], "size": [820.429931640625, 981.851806640625], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 386}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ComfyUI", ""], "color": "#223", "bgcolor": "#335"}, {"id": 241, "type": "CLIPTextEncode", "pos": [3524.49609375, 1696.64501953125], "size": [385.7064514160156, 93], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 354}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 355}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [326, 344]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {"text": true}}, "widgets_values": ["Using this elegant style, create a portrait of a swan wearing a pearl tiara and lace collar, maintaining the same refined quality and soft color tones."], "color": "#232", "bgcolor": "#353"}, {"id": 201, "type": "ShowText|pysssss", "pos": [3388.808837890625, 417.0299987792969], "size": [384.5976867675781, 200.37255859375], "flags": {"pinned": true}, "order": 19, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "link": 318}], "outputs": [{"label": "STRING", "name": "STRING", "shape": 6, "type": "STRING"}], "properties": {"Node name for S&R": "ShowText|pysssss", "cnr_id": "comfyui-custom-scripts", "ver": "aac13aa7ce35b07d43633c3bbe654a38c00d74f5", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["Remove women's clothes", "Remove women's clothes"], "color": "#223", "bgcolor": "#335"}, {"id": 223, "type": "PMRF", "pos": [5205.22021484375, 772.7824096679688], "size": [315, 178], "flags": {}, "order": 47, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 338}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [325]}], "properties": {"Node name for S&R": "PMRF", "cnr_id": "ComfyUI-PMRF", "ver": "e105c042161785b6fbbae840e70817896c1eebf4", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2, 25, 3190127557, "randomize", "lanczos4"], "color": "#223", "bgcolor": "#335"}, {"id": 246, "type": "SaveImage", "pos": [3836.400634765625, 46.285118103027344], "size": [407.2988586425781, 299.3293762207031], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 361}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["T8star-Aix"], "color": "#223", "bgcolor": "#335"}, {"id": 228, "type": "PreviewImage", "pos": [4724.529296875, 2479.4912109375], "size": [140, 246], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 343}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 235, "type": "LoraLoaderModelOnly", "pos": [3397.8203125, 1373.6015625], "size": [270, 82], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 373}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [374]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["HyperSD-Accelerator-FLUX-PAseerV2.safetensors", 1], "color": "#223", "bgcolor": "#335"}, {"id": 253, "type": "LoraLoaderModelOnly", "pos": [3586.958740234375, 1192.1201171875], "size": [270, 82], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 368}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [339]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["HyperSD-Accelerator-FLUX-PAseerV2.safetensors", 1], "color": "#223", "bgcolor": "#335"}, {"id": 213, "type": "MZ_Flux1PartialLoad_Patch", "pos": [3202.20361328125, 1172.1201171875], "size": [330.2437438964844, 82], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 329}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [368]}], "properties": {"Node name for S&R": "MZ_Flux1PartialLoad_Patch", "cnr_id": "ComfyUI-FluxExt-MZ", "ver": "00e4b70dea2b1d9b3f99a4fd267687bc69bdda98", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [7, 7], "color": "#223", "bgcolor": "#335"}, {"id": 214, "type": "UNETLoader", "pos": [2198.161376953125, 1202.169189453125], "size": [337.76861572265625, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": []}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.38", "models": [{"name": "flux1-dev-kontext_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["flux1-dev-kontext_fp8_scaled.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 233, "type": "PreviewImage", "pos": [4159.68505859375, 2509.123046875], "size": [140, 246], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 347}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}], "links": [[318, 251, 0, 201, 0, "STRING"], [319, 202, 0, 204, 0, "CLIP"], [320, 251, 0, 204, 1, "STRING"], [321, 204, 0, 205, 0, "CONDITIONING"], [322, 207, 0, 206, 0, "IMAGE"], [323, 225, 0, 207, 0, "LATENT"], [324, 203, 0, 207, 1, "VAE"], [325, 223, 0, 208, 0, "IMAGE"], [326, 241, 0, 210, 0, "CONDITIONING"], [327, 234, 0, 211, 0, "LATENT"], [328, 203, 0, 211, 1, "VAE"], [329, 245, 0, 213, 0, "MODEL"], [330, 229, 0, 215, 0, "STRING"], [331, 212, 0, 216, 0, "IMAGE"], [332, 207, 0, 216, 1, "IMAGE"], [333, 244, 0, 217, 0, "IMAGE"], [334, 203, 0, 217, 1, "VAE"], [335, 204, 0, 218, 0, "CONDITIONING"], [336, 217, 0, 218, 1, "LATENT"], [337, 218, 0, 222, 0, "CONDITIONING"], [338, 211, 0, 223, 0, "IMAGE"], [339, 253, 0, 225, 0, "MODEL"], [340, 222, 0, 225, 1, "CONDITIONING"], [341, 205, 0, 225, 2, "CONDITIONING"], [342, 217, 0, 225, 3, "LATENT"], [343, 248, 0, 228, 0, "IMAGE"], [344, 241, 0, 230, 0, "CONDITIONING"], [345, 243, 0, 230, 1, "LATENT"], [347, 232, 0, 233, 0, "IMAGE"], [349, 249, 0, 234, 1, "CONDITIONING"], [350, 210, 0, 234, 2, "CONDITIONING"], [351, 225, 0, 234, 3, "LATENT"], [353, 211, 0, 239, 0, "IMAGE"], [354, 202, 0, 241, 0, "CLIP"], [355, 215, 0, 241, 1, "STRING"], [357, 248, 0, 243, 0, "IMAGE"], [358, 203, 0, 243, 1, "VAE"], [359, 212, 0, 244, 0, "IMAGE"], [360, 254, 0, 245, 0, "MODEL"], [361, 216, 0, 246, 0, "IMAGE"], [362, 207, 0, 248, 0, "IMAGE"], [363, 232, 0, 248, 1, "IMAGE"], [364, 230, 0, 249, 0, "CONDITIONING"], [365, 248, 0, 250, 0, "IMAGE"], [366, 209, 0, 251, 0, "STRING"], [368, 213, 0, 253, 0, "MODEL"], [369, 247, 0, 254, 0, "MODEL"], [373, 247, 0, 235, 0, "MODEL"], [374, 235, 0, 234, 0, "MODEL"], [375, 215, 0, 255, 0, "*"], [376, 256, 0, 232, 0, "IMAGE"], [377, 225, 0, 258, 0, "LATENT"], [378, 262, 0, 261, 0, "IMAGE"], [379, 260, 0, 262, 0, "LATENT"], [380, 259, 0, 262, 1, "VAE"], [381, 212, 0, 263, 0, "IMAGE"], [382, 256, 0, 263, 1, "IMAGE"], [384, 211, 0, 265, 1, "IMAGE"], [385, 263, 0, 265, 0, "IMAGE"], [386, 265, 0, 266, 0, "IMAGE"]], "groups": [{"id": 7, "title": "B站、Youtube：T8star-Aix", "bounding": [1973.380126953125, -618.5014038085938, 4168, 309], "color": "#3f789e", "font_size": 240, "flags": {}}, {"id": 8, "title": "本地解码", "bounding": [1925.6041259765625, 2455.82666015625, 1033.34814453125, 251.86563110351562], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 1.0610764609500152, "offset": [-3681.0115065329464, -1909.461366822937]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "groupNodes": {}, "node_versions": {"comfy-core": "0.3.42", "ComfyUI-MagCache": "d53d144466f4abbbe105b6045730df8141ede8b4"}}, "version": 0.4}