{"id": "7cbcec68-7fa6-47bb-a38a-da689949a001", "revision": 0, "last_node_id": 198, "last_link_id": 301, "nodes": [{"id": 150, "type": "ImageStitch", "pos": [300, 1300], "size": [270, 150], "flags": {}, "order": 10, "mode": 0, "inputs": [{"localized_name": "image1", "name": "image1", "type": "IMAGE", "link": 255}, {"localized_name": "image2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 256}, {"localized_name": "direction", "name": "direction", "type": "COMBO", "widget": {"name": "direction"}, "link": null}, {"localized_name": "match_image_size", "name": "match_image_size", "type": "BOOLEAN", "widget": {"name": "match_image_size"}, "link": null}, {"localized_name": "spacing_width", "name": "spacing_width", "type": "INT", "widget": {"name": "spacing_width"}, "link": null}, {"localized_name": "spacing_color", "name": "spacing_color", "type": "COMBO", "widget": {"name": "spacing_color"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [257, 273]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ImageStitch", "widget_ue_connectable": {}}, "widgets_values": ["right", true, 0, "white"]}, {"id": 186, "type": "Reroute", "pos": [847.5836791992188, 1443.1756591796875], "size": [75, 26], "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 299}], "outputs": [{"name": "", "type": "LATENT", "links": [288]}], "properties": {"showOutputText": false, "horizontal": false, "widget_ue_connectable": {}}}, {"id": 187, "type": "ReferenceLatent", "pos": [1222.************, 1350.4102783203125], "size": [197.712890625, 46], "flags": {}, "order": 15, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 287}, {"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 288}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [295]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "ReferenceLatent", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 190, "type": "FluxGuidance", "pos": [1432.************, 1350.4102783203125], "size": [211.60000610351562, 58], "flags": {}, "order": 16, "mode": 0, "inputs": [{"localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 295}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [292]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxGuidance", "widget_ue_connectable": {}}, "widgets_values": [2.5]}, {"id": 196, "type": "VAELoader", "pos": [1452.************, 1200.4102783203125], "size": [270, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [290]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 188, "type": "VAEDecode", "pos": [1755.************, 1014.************], "size": [210, 46], "flags": {}, "order": 18, "mode": 0, "inputs": [{"localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 289}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 290}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [300]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 194, "type": "UNETLoader", "pos": [1122.************, 1200.4102783203125], "size": [270, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "UNet名称", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "数据类型", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [291]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["flux1-dev-kontext_fp8_scaled.safetensors", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 124, "type": "VAEEncode", "pos": [299.1706848144531, 1102.8865966796875], "size": [270, 50], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 222}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 223}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [299]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEEncode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 42, "type": "FluxKontextImageScale", "pos": [305.80548095703125, 1207.607666015625], "size": [270, 30], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 257}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [222]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 152, "type": "LoadImage", "pos": [-70, 1650], "size": [350, 400], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [256]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["制作大白菜图片.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "VAELoader", "pos": [-65.93605041503906, 1102.8951416015625], "size": [336.1098937988281, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [223]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 172, "type": "PreviewImage", "pos": [618.1625366210938, 743.6187133789062], "size": [626.6707763671875, 386.1868591308594], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "图像", "name": "images", "type": "IMAGE", "link": 273}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 198, "type": "Note", "pos": [-556.7947998046875, 1111.659423828125], "size": [459.3904113769531, 939.2797241210938], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "提示词技巧", "properties": {}, "widgets_values": ["\n## Flux Kontext 提示词技巧\n\n使用英文\n\n### 1. 基础修改\n- 简单直接：`\"Change the car color to red\"`\n- 保持风格：`\"Change to daytime while maintaining the same style of the painting\"`\n\n### 2. 风格转换\n**原则：**\n- 明确命名风格：`\"Transform to Bauhaus art style\"`\n- 描述特征：`\"Transform to oil painting with visible brushstrokes, thick paint texture\"`\n- 保留构图：`\"Change to Bauhaus style while maintaining the original composition\"`\n\n### 3. 角色一致性\n**框架：**\n- 具体描述：`\"The woman with short black hair\"`而非`\"她\"`\n- 保留特征：`\"while maintaining the same facial features, hairstyle, and expression\"`\n- 分步修改：先改背景，再改动作\n\n### 4. 文本编辑\n- 使用引号：`\"Replace 'joy' with 'BFL'\"`\n- 保持格式：`\"Replace text while maintaining the same font style\"`\n\n## 常见问题解决\n\n### 角色变化过大\n❌ 错误：`\"Transform the person into a Viking\"`\n✅ 正确：`\"Change the clothes to be a viking warrior while preserving facial features\"`\n\n### 构图位置改变\n❌ 错误：`\"Put him on a beach\"`\n✅ 正确：`\"Change the background to a beach while keeping the person in the exact same position, scale, and pose\"`\n\n### 风格应用不准确\n❌ 错误：`\"Make it a sketch\"`\n✅ 正确：`\"Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture\"`\n\n## 核心原则\n\n1. **具体明确** - 使用精确描述，避免模糊词汇\n2. **分步编辑** - 复杂修改分为多个简单步骤\n3. **明确保留** - 说明哪些要保持不变\n4. **动词选择** - 用\"更改\"、\"替换\"而非\"转换\"\n\n## 最佳实践模板\n\n**对象修改：**\n`\"Change [object] to [new state], keep [content to preserve] unchanged\"`\n\n**风格转换：**\n`\"Transform to [specific style], while maintaining [composition/character/other] unchanged\"`\n\n**背景替换：**\n`\"Change the background to [new background], keep the subject in the exact same position and pose\"`\n\n**文本编辑：**\n`\"Replace '[original text]' with '[new text]', maintain the same font style\"`\n\n> **记住：** 越具体越好，Kontext 擅长理解详细指令并保持一致性。"], "color": "#432", "bgcolor": "#653"}, {"id": 144, "type": "SaveImage", "pos": [1851.7564697265625, 1155.9932861328125], "size": [912.2810668945312, 993.245849609375], "flags": {}, "order": 19, "mode": 0, "inputs": [{"localized_name": "图片", "name": "images", "type": "IMAGE", "link": 300}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}, {"id": 195, "type": "DualCLIPLoader", "pos": [592.************, 1280.4102783203125], "size": [337.76861572265625, 130], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [297, 298]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "DualCLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["t5xxl_fp8_e4m3fn.safetensors", "clip_l.safetensors", "flux", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 193, "type": "CLIPTextEncode", "pos": [1454.068359375, 1136.340576171875], "size": [422.84503173828125, 164.31304931640625], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 298}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [293]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": [""], "color": "#322", "bgcolor": "#533"}, {"id": 197, "type": "EmptySD3LatentImage", "pos": [1108.0145263671875, 1467.52880859375], "size": [270, 106], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [301]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "EmptySD3LatentImage", "widget_ue_connectable": {}}, "widgets_values": [1024, 1024, 1]}, {"id": 189, "type": "K<PERSON><PERSON><PERSON>", "pos": [1410.3563232421875, 1468.0455322265625], "size": [404.1548156738281, 660.15478515625], "flags": {}, "order": 17, "mode": 0, "inputs": [{"localized_name": "模型", "name": "model", "type": "MODEL", "link": 291}, {"localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 292}, {"localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 293}, {"localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 301}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [289]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": [526306791717558, "randomize", 20, 1, "euler", "simple", 1]}, {"id": 133, "type": "LoadImage", "pos": [-70, 1250], "size": [340, 350], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [255]}, {"localized_name": "遮罩", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_00013_.png", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 192, "type": "CLIPTextEncode", "pos": [347.7182922363281, 1563.890625], "size": [547.2470092773438, 469.663330078125], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 297}, {"localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [287]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["A woman  holds the cabbage in images 2 in her hand while maintaining her same facial ID"], "color": "#232", "bgcolor": "#353"}], "links": [[222, 42, 0, 124, 0, "IMAGE"], [223, 39, 0, 124, 1, "VAE"], [255, 133, 0, 150, 0, "IMAGE"], [256, 152, 0, 150, 1, "IMAGE"], [257, 150, 0, 42, 0, "IMAGE"], [273, 150, 0, 172, 0, "IMAGE"], [287, 192, 0, 187, 0, "CONDITIONING"], [288, 186, 0, 187, 1, "LATENT"], [289, 189, 0, 188, 0, "LATENT"], [290, 196, 0, 188, 1, "VAE"], [291, 194, 0, 189, 0, "MODEL"], [292, 190, 0, 189, 1, "CONDITIONING"], [293, 193, 0, 189, 2, "CONDITIONING"], [295, 187, 0, 190, 0, "CONDITIONING"], [297, 195, 0, 192, 0, "CLIP"], [298, 195, 0, 193, 0, "CLIP"], [299, 124, 0, 186, 0, "*"], [300, 188, 0, 144, 0, "IMAGE"], [301, 197, 0, 189, 3, "LATENT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.7972024500000099, "offset": [-333.3923897664988, -1105.9911280739898]}, "frontendVersion": "1.23.2", "groupNodes": {"Edit": {"nodes": [{"id": -1, "type": "Reroute", "pos": [2354.87890625, -127.23468780517578], "size": [75, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}, "index": 0}, {"id": -1, "type": "ReferenceLatent", "pos": [2730, -220], "size": [197.712890625, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": []}], "properties": {"Node name for S&R": "ReferenceLatent", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 1, "widgets_values": []}, {"id": -1, "type": "VAEDecode", "pos": [3270, -110], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": null}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 2, "widgets_values": []}, {"id": -1, "type": "K<PERSON><PERSON><PERSON>", "pos": [2930, -110], "size": [315, 262], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": null}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": null}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": null}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [972054013131369, "fixed", 20, 1, "euler", "simple", 1], "index": 3}, {"id": -1, "type": "FluxGuidance", "pos": [2940, -220], "size": [211.60000610351562, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "guidance", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "FluxGuidance", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [2.5], "index": 4}, {"id": -1, "type": "SaveImage", "pos": [3490, -110], "size": [985.3012084960938, 1060.3828125], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ComfyUI"], "index": 5}, {"id": -1, "type": "CLIPTextEncode", "pos": [2500, -110], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["there is a bright light"], "color": "#232", "bgcolor": "#353", "index": 6}, {"id": -1, "type": "CLIPTextEncode", "pos": [2504.1435546875, 97.9598617553711], "size": [422.84503173828125, 164.31304931640625], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533", "index": 7}, {"id": -1, "type": "UNETLoader", "pos": [2630, -370], "size": [270, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "weight_dtype", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": []}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["preliminary-dev-kontext.sft", "default"], "color": "#223", "bgcolor": "#335", "index": 8}, {"id": -1, "type": "DualCLIPLoader", "pos": [2100, -290], "size": [337.76861572265625, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}], "properties": {"Node name for S&R": "DualCLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"], "color": "#223", "bgcolor": "#335", "index": 9}, {"id": -1, "type": "VAELoader", "pos": [2960, -370], "size": [270, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "vae_name", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": []}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ae.safetensors"], "color": "#223", "bgcolor": "#335", "index": 10}], "links": [[6, 0, 1, 0, 72, "CONDITIONING"], [0, 0, 1, 1, 66, "*"], [3, 0, 2, 0, 69, "LATENT"], [10, 0, 2, 1, 76, "VAE"], [8, 0, 3, 0, 74, "MODEL"], [4, 0, 3, 1, 70, "CONDITIONING"], [7, 0, 3, 2, 73, "CONDITIONING"], [0, 0, 3, 3, 66, "*"], [1, 0, 4, 0, 67, "CONDITIONING"], [2, 0, 5, 0, 68, "IMAGE"], [9, 0, 6, 0, 75, "CLIP"], [9, 0, 7, 0, 75, "CLIP"]], "external": [], "config": {"0": {}, "1": {}, "2": {"output": {"0": {"visible": true}}}, "3": {"output": {"0": {"visible": true}}, "input": {"denoise": {"visible": false}, "cfg": {"visible": false}}}, "4": {}, "5": {}, "6": {}, "7": {"input": {"text": {"visible": false}}}, "8": {}, "9": {"input": {"type": {"visible": false}}}, "10": {}}}, "FLUX.1 Kontext Image Edit": {"nodes": [{"id": -1, "type": "Reroute", "pos": [2354.87890625, -127.23468780517578], "size": [75, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}, "index": 0}, {"id": -1, "type": "ReferenceLatent", "pos": [2730, -220], "size": [197.712890625, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": []}], "properties": {"Node name for S&R": "ReferenceLatent", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 1, "widgets_values": []}, {"id": -1, "type": "VAEDecode", "pos": [3270, -110], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": null}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 2, "widgets_values": []}, {"id": -1, "type": "K<PERSON><PERSON><PERSON>", "pos": [2930, -110], "size": [315, 262], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": null}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": null}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": null}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [972054013131369, "fixed", 20, 1, "euler", "simple", 1], "index": 3}, {"id": -1, "type": "FluxGuidance", "pos": [2940, -220], "size": [211.60000610351562, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "guidance", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "FluxGuidance", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [2.5], "index": 4}, {"id": -1, "type": "SaveImage", "pos": [3490, -110], "size": [985.3012084960938, 1060.3828125], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ComfyUI"], "index": 5}, {"id": -1, "type": "CLIPTextEncode", "pos": [2500, -110], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["there is a bright light"], "color": "#232", "bgcolor": "#353", "index": 6}, {"id": -1, "type": "CLIPTextEncode", "pos": [2504.1435546875, 97.9598617553711], "size": [422.84503173828125, 164.31304931640625], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533", "index": 7}, {"id": -1, "type": "UNETLoader", "pos": [2630, -370], "size": [270, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "weight_dtype", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": []}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["preliminary-dev-kontext.sft", "default"], "color": "#223", "bgcolor": "#335", "index": 8}, {"id": -1, "type": "DualCLIPLoader", "pos": [2100, -290], "size": [337.76861572265625, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}], "properties": {"Node name for S&R": "DualCLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp16.safetensors", "flux", "default"], "color": "#223", "bgcolor": "#335", "index": 9}, {"id": -1, "type": "VAELoader", "pos": [2960, -370], "size": [270, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "vae_name", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": []}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ae.safetensors"], "color": "#223", "bgcolor": "#335", "index": 10}], "links": [[6, 0, 1, 0, 72, "CONDITIONING"], [0, 0, 1, 1, 66, "*"], [3, 0, 2, 0, 69, "LATENT"], [10, 0, 2, 1, 76, "VAE"], [8, 0, 3, 0, 74, "MODEL"], [4, 0, 3, 1, 70, "CONDITIONING"], [7, 0, 3, 2, 73, "CONDITIONING"], [0, 0, 3, 3, 66, "*"], [1, 0, 4, 0, 67, "CONDITIONING"], [2, 0, 5, 0, 68, "IMAGE"], [9, 0, 6, 0, 75, "CLIP"], [9, 0, 7, 0, 75, "CLIP"]], "external": [], "config": {"0": {}, "1": {}, "2": {"output": {"0": {"visible": true}}}, "3": {"output": {"0": {"visible": true}}, "input": {"denoise": {"visible": false}, "cfg": {"visible": false}}}, "4": {}, "5": {}, "6": {}, "7": {"input": {"text": {"visible": false}}}, "8": {}, "9": {"input": {"type": {"visible": false}}}, "10": {}}}}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "ue_links": [], "links_added_by_ue": []}, "version": 0.4}