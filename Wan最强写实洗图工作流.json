{"id": "25dfce61-3571-4c9a-a7f8-6a83951d66aa", "revision": 0, "last_node_id": 134, "last_link_id": 260, "nodes": [{"id": 68, "type": "LayerUtility: ImageReel", "pos": [4008.564208984375, -470.7314147949219], "size": [270, 238], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 212}, {"label": "image2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 221}, {"label": "image3", "name": "image3", "shape": 7, "type": "IMAGE"}, {"label": "image4", "name": "image4", "shape": 7, "type": "IMAGE"}], "outputs": [{"label": "reel", "name": "reel", "type": "<PERSON><PERSON>", "links": [100]}], "properties": {"Node name for S&R": "LayerUtility: ImageReel", "cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f"}, "widgets_values": ["原图", "洗图", "", "image4", 1536, 8], "color": "#323", "bgcolor": "#535"}, {"id": 119, "type": "CLIPTextEncode", "pos": [2978.415283203125, -244.60467529296875], "size": [450, 150], "flags": {"collapsed": false}, "order": 14, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 253}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [220]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#232", "bgcolor": "#353"}, {"id": 118, "type": "CLIPTextEncode", "pos": [2974.2880859375, -464.5184631347656], "size": [437.6938171386719, 146.9487762451172], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 252}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 217}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [219]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果, 描绘了一位年轻女性脸部特写, 被透过薄纱窗帘的金色晨光照亮, 从侧面拍摄.\n在紧凑的1:1特写镜头中, 仅可见女性脸庞的一半, 略微转向附近的一扇窗户, 温暖, 清晨的阳光透过白色布帘倾泻而入.光线柔和地掠过她的面容, 突显出她脸颊上细腻的桃色绒毛和嘴唇附近干燥皮肤的微弱纹理.她的眼睛在画框边缘刚刚可见, 捕捉到一丝琥珀色光芒.漂浮在空气中的杂乱发丝在逆光中发光.背景模糊不清但显然是室内--可能是卧室--失焦并染上日黄色调.她表情中的轻微眯眼可见清晨的昏沉感.镜头在她脸部最亮的部分周围形成淡淡的光晕, 光线最强的区域有轻微的色彩溢出.感觉非常亲密, 像是醒来后拍下的第一张照片, 在寂静中捕捉."], "color": "#232", "bgcolor": "#353"}, {"id": 96, "type": "VAEEncode", "pos": [3460.310546875, -313.3268127441406], "size": [241.07766723632812, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "pixels", "name": "pixels", "type": "IMAGE", "link": 190}, {"label": "vae", "name": "vae", "type": "VAE", "link": 258}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [173]}], "properties": {"Node name for S&R": "VAEEncode", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 79, "type": "CFGZeroStarAndInit", "pos": [3440.072509765625, -466.7919921875], "size": [270, 82], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 251}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [133]}], "properties": {"Node name for S&R": "CFGZeroStarAndInit", "cnr_id": "comfyui-kjnodes", "ver": "d9425173e77b5be8c75492a72424ddffae4d4445"}, "widgets_values": [true, 0], "color": "#323", "bgcolor": "#535"}, {"id": 67, "type": "CR Text Concatenate", "pos": [1560.86376953125, -457.8575439453125], "size": [343.8714904785156, 126], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "text1", "name": "text1", "shape": 7, "type": "STRING", "link": 91}, {"label": "text2", "name": "text2", "shape": 7, "type": "STRING", "link": 176}], "outputs": [{"label": "STRING", "name": "STRING", "type": "*", "links": [217]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text Concatenate", "cnr_id": "comfyroll", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca"}, "widgets_values": ["，", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 63, "type": "CR Text", "pos": [1193.3836669921875, -463.0902404785156], "size": [327.2252502441406, 113], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [91]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "cnr_id": "comfyroll", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca"}, "widgets_values": ["2010 年代初期用手机拍摄并上传到 Facebook 的快照照片，具有动态自然光和中性白平衡和褪色颜色，亚洲女性"], "color": "#232", "bgcolor": "#353"}, {"id": 98, "type": "easy showAnything", "pos": [1560.2052001953125, -287.0629577636719], "size": [351.6213073730469, 373.6241455078125], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "shape": 7, "type": "*", "link": 243}], "outputs": [{"label": "output", "name": "output", "type": "*", "links": [176]}], "properties": {"Node name for S&R": "easy showAnything", "cnr_id": "comfyui-easy-use", "ver": "e7320ec0c463641f702add48b87363c7a9713a1c"}, "widgets_values": ["The image depicts a woman with long, straight, dark hair wearing a sleeveless white dress made of a delicate lace-like fabric, which gives her outfit an elegant and feminine appearance. She holds a bouquet of fresh white roses with dark green stems and leaves, carefully clasped in both hands. The background is dimly lit, suggesting an indoor setting with soft, diffused light. A vague rectangular object, possibly a picture frame or decor, is visible hanging on the wall in the blurred background. The overall mood of the image evokes serenity and thoughtfulness, with a muted color palette that emphasizes the contrast between the pure white dress and roses against the darker backdrop. The inclusion of white roses, symbolizing purity or remembrance, adds emotional depth, suggesting themes of peace, love, or mourning."], "color": "#232", "bgcolor": "#353"}, {"id": 105, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [865.7685546875, -474.0211486816406], "size": [336, 330], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 225}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [190]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT"}, {"label": "height", "name": "height", "type": "INT"}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2", "cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 1536, "#000000"], "color": "#223", "bgcolor": "#335"}, {"id": 126, "type": "RH_Captioner", "pos": [1150.839111328125, -280.4539794921875], "size": [364.04083251953125, 196.65501403808594], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "type": "IMAGE", "link": 244}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [243]}], "properties": {"Node name for S&R": "RH_Captioner", "widget_ue_connectable": {}}, "widgets_values": ["Please provide a detailed description of this image. If any characters in the image are familiar to you, such as celebrities, movie characters, or animated figures, please directly use their names. The description should be as detailed as possible, but should not exceed 200 words."], "color": "#223", "bgcolor": "#335"}, {"id": 128, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2470, -470], "size": [413.3301086425781, 133.2881317138672], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 245}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 256}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [246]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [247]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4000000000000001, 0.4000000000000001], "color": "#332922", "bgcolor": "#593930"}, {"id": 129, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2460, -270], "size": [438.8387451171875, 140.66421508789062], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 246}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 247}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [248]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [249]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 0.4000000000000001, 0.4000000000000001], "color": "#332922", "bgcolor": "#593930"}, {"id": 130, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2470, -60], "size": [434.2865295410156, 126], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 248}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 249}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [251]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [252, 253]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": ["WAN2.1_SmartphoneSnapshotPhotoReality_v1_by-AI_Characters.safetensors", 1.0000000000000002, 1.0000000000000002], "color": "#332922", "bgcolor": "#593930"}, {"id": 127, "type": "UNETLoader", "pos": [1996.2032470703125, -449.1222229003906], "size": [420.1534118652344, 83.15281677246094], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [245]}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan2.1_t2v_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors?download=true", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["Wan2_1-T2V-14B_fp8_e4m3fn.safetensors", "fp8_e4m3fn"], "color": "#332922", "bgcolor": "#593930"}, {"id": 131, "type": "CLIPLoader", "pos": [1990.7254638671875, -255.4075164794922], "size": [360.6138610839844, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [256]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 132, "type": "VAELoader", "pos": [2005.287109375, -57.278892517089844], "size": [367.7924499511719, 70.40070343017578], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [257, 258]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 95, "type": "LoadImage", "pos": [1972.613037109375, 223.54556274414062], "size": [556.7559814453125, 898.8511352539062], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [212, 225, 238, 244]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["63f99715daabff59fc24bbe648670dc13e2433604f9880654c16aead7f85ba54.png", "image", ""], "color": "#232", "bgcolor": "#353"}, {"id": 46, "type": "VAEDecode", "pos": [3469.150146484375, -181.0399932861328], "size": [246.62803649902344, 59.8025016784668], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 62}, {"label": "vae", "name": "vae", "type": "VAE", "link": 257}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [221, 259]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 133, "type": "SaveImage", "pos": [2600.965087890625, 230.9801788330078], "size": [547.776611328125, 891.9474487304688], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 259}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI", ""], "color": "#232", "bgcolor": "#353"}, {"id": 69, "type": "LayerUtility: ImageReelComposit", "pos": [4010.09423828125, -176.093505859375], "size": [277.20001220703125, 190], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "reel_1", "name": "reel_1", "type": "<PERSON><PERSON>", "link": 100}, {"label": "reel_2", "name": "reel_2", "shape": 7, "type": "<PERSON><PERSON>"}, {"label": "reel_3", "name": "reel_3", "shape": 7, "type": "<PERSON><PERSON>"}, {"label": "reel_4", "name": "reel_4", "shape": 7, "type": "<PERSON><PERSON>"}], "outputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "links": [260]}], "properties": {"Node name for S&R": "LayerUtility: ImageReelComposit", "cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f"}, "widgets_values": ["Alibaba-PuHuiTi-Heavy.ttf", 40, 8, "dark"], "color": "#323", "bgcolor": "#535"}, {"id": 134, "type": "SaveImage", "pos": [3182.285888671875, 234.0728302001953], "size": [1128.177001953125, 873.9366455078125], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 260}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI", ""], "color": "#232", "bgcolor": "#353"}, {"id": 48, "type": "K<PERSON><PERSON><PERSON>", "pos": [3734.138916015625, -474.2762145996094], "size": [250, 474], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 133}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 219}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 220}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 173}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [62]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": [389343985507363, "randomize", 8, 1, "res_2s", "bong_tangent", 0.6000000000000001], "color": "#323", "bgcolor": "#535"}], "links": [[62, 48, 0, 46, 0, "LATENT"], [91, 63, 0, 67, 0, "STRING"], [100, 68, 0, 69, 0, "<PERSON><PERSON>"], [133, 79, 0, 48, 0, "MODEL"], [173, 96, 0, 48, 3, "LATENT"], [176, 98, 0, 67, 1, "STRING"], [190, 105, 0, 96, 0, "IMAGE"], [212, 95, 0, 68, 0, "IMAGE"], [217, 67, 0, 118, 1, "STRING"], [219, 118, 0, 48, 1, "CONDITIONING"], [220, 119, 0, 48, 2, "CONDITIONING"], [221, 46, 0, 68, 1, "IMAGE"], [225, 95, 0, 105, 0, "IMAGE"], [243, 126, 0, 98, 0, "*"], [244, 95, 0, 126, 0, "IMAGE"], [245, 127, 0, 128, 0, "MODEL"], [246, 128, 0, 129, 0, "MODEL"], [247, 128, 1, 129, 1, "CLIP"], [248, 129, 0, 130, 0, "MODEL"], [249, 129, 1, 130, 1, "CLIP"], [251, 130, 0, 79, 0, "MODEL"], [252, 130, 1, 118, 0, "CLIP"], [253, 130, 1, 119, 0, "CLIP"], [256, 131, 0, 128, 1, "CLIP"], [257, 132, 0, 46, 1, "VAE"], [258, 132, 0, 96, 1, "VAE"], [259, 46, 0, 133, 0, "IMAGE"], [260, 69, 0, 134, 0, "IMAGE"]], "groups": [{"id": 1, "title": "B站、YouTube、公众号：嘟嘟AI绘画趣味学", "bounding": [1741.321533203125, -769.2910766601562, 2016.12744140625, 132.8913116455078], "color": "#b06634", "font_size": 100, "flags": {}}, {"id": 2, "title": "提示词封装", "bounding": [845.7685546875, -558.0211181640625, 1090.1766357421875, 709.272705078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "模型", "bounding": [1954.2213134765625, -551.720703125, 970, 654], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "采样", "bounding": [2954.2880859375, -558.276123046875, 1345.80615234375, 592.1827392578125], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8769226950000023, "offset": [-1383.6753757150439, 771.0949491207667]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "workspace_info": {"id": "2DPvFpHLLMzmEMupNJKw3"}, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false}, "version": 0.4}