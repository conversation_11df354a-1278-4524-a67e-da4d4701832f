{"id": "7cbcec68-7fa6-47bb-a38a-da689949a001", "revision": 0, "last_node_id": 193, "last_link_id": 299, "nodes": [{"id": 39, "type": "VAELoader", "pos": [-397.5133972167969, 383.81854248046875], "size": [337.76861572265625, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [61, 223]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 135, "type": "ConditioningZeroOut", "pos": [250, 200], "size": [240, 26], "flags": {"collapsed": false}, "order": 9, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 237}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [238]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "ConditioningZeroOut", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 173, "type": "PreviewImage", "pos": [320, 860], "size": [420, 310], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 289}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 147, "type": "LoadImageOutput", "pos": [-50, 770], "size": [320, 374], "flags": {}, "order": 1, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [250]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImageOutput", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_00189_.png [output]", false, "refresh", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 8, "type": "VAEDecode", "pos": [530, 350], "size": [190, 46], "flags": {"collapsed": false}, "order": 16, "mode": 0, "inputs": [{"label": "samples", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 52}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 61}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [240]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 124, "type": "VAEEncode", "pos": [-20, 400], "size": [240, 50], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"label": "pixels", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 222}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 223}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [291, 293]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEEncode", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 42, "type": "FluxKontextImageScale", "pos": [-50, 570], "size": [270, 30], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 251}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [222, 289]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 35, "type": "FluxGuidance", "pos": [250, 90], "size": [240, 58], "flags": {"collapsed": false}, "order": 14, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 292}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxGuidance", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2.5], "color": "#223", "bgcolor": "#335"}, {"id": 177, "type": "ReferenceLatent", "pos": [10, 140], "size": [211.60000610351562, 46], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 294}, {"label": "latent", "localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 293}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [292]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ReferenceLatent", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 188, "type": "EmptySD3LatentImage", "pos": [530, -140], "size": [310, 106], "flags": {}, "order": 2, "mode": 4, "inputs": [{"localized_name": "宽度", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": null}, {"localized_name": "高度", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": null}, {"localized_name": "批量大小", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": null}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "EmptySD3LatentImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [1024, 1024, 1], "color": "#223", "bgcolor": "#335"}, {"id": 38, "type": "DualCLIPLoader", "pos": [-397.5133972167969, 203.8184356689453], "size": [337.76861572265625, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [59]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "DualCLIPLoader", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 146, "type": "ImageStitch", "pos": [-390, 570], "size": [270, 150], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "image1", "localized_name": "image1", "name": "image1", "type": "IMAGE", "link": 296}, {"label": "image2", "localized_name": "image2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 250}, {"localized_name": "direction", "name": "direction", "type": "COMBO", "widget": {"name": "direction"}, "link": null}, {"localized_name": "match_image_size", "name": "match_image_size", "type": "BOOLEAN", "widget": {"name": "match_image_size"}, "link": null}, {"localized_name": "spacing_width", "name": "spacing_width", "type": "INT", "widget": {"name": "spacing_width"}, "link": null}, {"localized_name": "spacing_color", "name": "spacing_color", "type": "COMBO", "widget": {"name": "spacing_color"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [251]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "ImageStitch", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["right", true, 0, "white"], "color": "#223", "bgcolor": "#335"}, {"id": 136, "type": "SaveImage", "pos": [993.2583618164062, 511.5040588378906], "size": [650, 660], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 240}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ComfyUI"], "color": "#223", "bgcolor": "#335"}, {"id": 31, "type": "K<PERSON><PERSON><PERSON>", "pos": [584.1874389648438, 18.95220375061035], "size": [320, 474], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 295}, {"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 57}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 238}, {"label": "latent_image", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 291}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [748140923970058, "randomize", 20, 1, "euler", "simple", 1], "color": "#223", "bgcolor": "#335"}, {"id": 175, "type": "<PERSON>downNote", "pos": [-61.859683990478516, 642.7366943359375], "size": [320, 88], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "About Flux Kontext Edit", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["[English] Use Ctrl + B to enable multipule image input.\n\n[中文] 使用 **Ctrl + B** 来启用多张图片输入"], "color": "#432", "bgcolor": "#653"}, {"id": 191, "type": "LoadImage", "pos": [-373.5699768066406, 801.457275390625], "size": [270, 314], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [296]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "LoadImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ComfyUI_00013_.png", "image"], "color": "#223", "bgcolor": "#335"}, {"id": 189, "type": "NunchakuFluxDiTLoader", "pos": [-385.71734619140625, -62.72630310058594], "size": [275.7613220214844, 202], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "model_path", "name": "model_path", "type": "COMBO", "widget": {"name": "model_path"}, "link": null}, {"localized_name": "cache_threshold", "name": "cache_threshold", "type": "FLOAT", "widget": {"name": "cache_threshold"}, "link": null}, {"localized_name": "attention", "name": "attention", "type": "COMBO", "widget": {"name": "attention"}, "link": null}, {"localized_name": "cpu_offload", "name": "cpu_offload", "type": "COMBO", "widget": {"name": "cpu_offload"}, "link": null}, {"localized_name": "device_id", "name": "device_id", "type": "INT", "widget": {"name": "device_id"}, "link": null}, {"localized_name": "data_type", "name": "data_type", "type": "COMBO", "widget": {"name": "data_type"}, "link": null}, {"localized_name": "i2f_mode", "name": "i2f_mode", "shape": 7, "type": "COMBO", "widget": {"name": "i2f_mode"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [295]}], "properties": {"cnr_id": "ComfyUI-nunchaku", "ver": "3b2c771cf2f4e62f97c284bfd8f594482c5f8bc0", "Node name for S&R": "NunchakuFluxDiTLoader", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["svdq-int4_r32-flux.1-kontext-dev.safetensors", 0.10000000000000002, "nunchaku-fp16", "auto", 0, "float16", "enabled"], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "CLIPTextEncode", "pos": [330, 560], "size": [400, 220], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 59}, {"label": "text", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [237, 294]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["Make the character's hair turn white", [false, true]], "color": "#232", "bgcolor": "#353"}], "links": [[52, 31, 0, 8, 0, "LATENT"], [57, 35, 0, 31, 1, "CONDITIONING"], [59, 38, 0, 6, 0, "CLIP"], [61, 39, 0, 8, 1, "VAE"], [222, 42, 0, 124, 0, "IMAGE"], [223, 39, 0, 124, 1, "VAE"], [237, 6, 0, 135, 0, "CONDITIONING"], [238, 135, 0, 31, 2, "CONDITIONING"], [240, 8, 0, 136, 0, "IMAGE"], [250, 147, 0, 146, 1, "IMAGE"], [251, 146, 0, 42, 0, "IMAGE"], [289, 42, 0, 173, 0, "IMAGE"], [291, 124, 0, 31, 3, "LATENT"], [292, 177, 0, 35, 0, "CONDITIONING"], [293, 124, 0, 177, 1, "LATENT"], [294, 6, 0, 177, 0, "CONDITIONING"], [295, 189, 0, 31, 0, "MODEL"], [296, 191, 0, 146, 0, "IMAGE"]], "groups": [{"id": 3, "title": "Step 2 - Upload images", "bounding": [-410, 480, 700, 680], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Step 3 - Prompt", "bounding": [310, 480, 430, 330], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Conditioning", "bounding": [-30, 10, 540, 250], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "B站、Youtube：T8star-Aix", "bounding": [-1549.734130859375, -712.2920532226562, 4168, 309], "color": "#3f789e", "font_size": 240, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8769226950000005, "offset": [1242.9954640108278, -201.84796676422033]}, "frontendVersion": "1.23.4", "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "groupNodes": {}}, "version": 0.4}