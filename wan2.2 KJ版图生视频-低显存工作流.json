{"id": "c6e410bc-5e2c-460b-ae81-c91b6094fbb1", "revision": 0, "last_node_id": 137, "last_link_id": 250, "nodes": [{"id": 108, "type": "Note", "pos": [-255.76266479492188, -879.6133422851562], "size": [303.0501403808594, 88], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["If you have Triton installed, connect this for ~30% speed increase"], "color": "#432", "bgcolor": "#653"}, {"id": 116, "type": "WanVideoVAELoader", "pos": [1735.5567626953125, -1333.44189453125], "size": [315, 82], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [207, 227]}], "properties": {"Node name for S&R": "WanVideoVAELoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e"}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 119, "type": "LoadWanVideoT5TextEncoder", "pos": [973.703369140625, -104.39110565185547], "size": [377.1661376953125, 130], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [220]}], "properties": {"Node name for S&R": "LoadWanVideoT5TextEncoder", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e"}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#332922", "bgcolor": "#593930"}, {"id": 120, "type": "ImageResizeKJv2", "pos": [1222.24755859375, -1327.00048828125], "size": [270, 336], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 212}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [208, 213]}, {"label": "width", "name": "width", "type": "INT", "links": [209]}, {"label": "height", "name": "height", "type": "INT", "links": [210]}, {"label": "mask", "name": "mask", "type": "MASK"}], "properties": {"Node name for S&R": "ImageResizeKJv2", "cnr_id": "comfyui-kjnodes", "ver": "a6b867b63a29ca48ddb15c589e17a9f2d8530d57"}, "widgets_values": [480, 832, "lanc<PERSON>s", "crop", "0, 0, 0", "center", 32, "cpu"]}, {"id": 121, "type": "PreviewImage", "pos": [1540.8380126953125, -1295.2318115234375], "size": [140, 246], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 213}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.46"}, "widgets_values": []}, {"id": 127, "type": "WanVideoDecode", "pos": [3365.56591796875, -1133.8516845703125], "size": [315, 198], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 227}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 245}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [229]}], "properties": {"Node name for S&R": "WanVideoDecode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e"}, "widgets_values": [false, 272, 272, 144, 128, "default"], "color": "#322", "bgcolor": "#533"}, {"id": 131, "type": "easy globalSeed", "pos": [1773.0546875, -432.82830810546875], "size": [270, 130], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {"Node name for S&R": "easy globalSeed", "cnr_id": "comfyui-easy-use", "ver": "717092a3ceb51c474b5b3f77fc188979f0db9d67"}, "widgets_values": [443532653877019, true, "randomize", 333425505478565]}, {"id": 124, "type": "WanVideoTextEncode", "pos": [1587.4693603515625, -887.70166015625], "size": [474.3573303222656, 316.48370361328125], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "t5", "name": "t5", "shape": 7, "type": "WANTEXTENCODER", "link": 220}, {"label": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL"}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [238, 241]}], "properties": {"Node name for S&R": "WanVideoTextEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e"}, "widgets_values": ["镜头拉近，女子跳着妩媚的舞姿，", "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", true, false, "gpu"], "color": "#332922", "bgcolor": "#593930"}, {"id": 132, "type": "Display Any (rgthree)", "pos": [2168.806884765625, -274.5521240234375], "size": [303.0512390136719, 88], "flags": {}, "order": 15, "mode": 0, "inputs": [{"dir": 3, "label": "source", "name": "source", "type": "*", "link": 230}], "outputs": [], "properties": {"Node name for S&R": "Display Any (rgthree)", "cnr_id": "rgthree-comfy", "ver": "944d5353a1b0a668f40844018c3dc956b95a67d7"}, "widgets_values": [""]}, {"id": 110, "type": "WanVideoLoraSelect", "pos": [265.8589782714844, -826.2522583007812], "size": [568.2962036132812, 155.64036560058594], "flags": {}, "order": 4, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [219]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e"}, "widgets_values": ["lightx2v_I2V_14B_480p_cfg_step_distill_rank64_bf16.safetensors", 2.0000000000000004, false, true], "color": "#223", "bgcolor": "#335"}, {"id": 136, "type": "WanVideoLoraSelect", "pos": [301.14862060546875, -453.8105163574219], "size": [568.2962036132812, 155.64036560058594], "flags": {}, "order": 5, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [246]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e", "widget_ue_connectable": {}}, "widgets_values": ["lightx2v_I2V_14B_480p_cfg_step_distill_rank64_bf16.safetensors", 1.0000000000000002, false, true], "color": "#223", "bgcolor": "#335"}, {"id": 117, "type": "WanVideoImageToVideoEncode", "pos": [2085.857177734375, -1338.81396484375], "size": [308.2320251464844, 390], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 207}, {"label": "clip_embeds", "name": "clip_embeds", "shape": 7, "type": "WANVIDIMAGE_CLIPEMBEDS"}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 208}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE"}, {"label": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "temporal_mask", "name": "temporal_mask", "shape": 7, "type": "MASK"}, {"label": "extra_latents", "name": "extra_latents", "shape": 7, "type": "LATENT"}, {"label": "add_cond_latents", "name": "add_cond_latents", "shape": 7, "type": "ADD_COND_LATENTS"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 209}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 210}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [247, 248]}], "properties": {"Node name for S&R": "WanVideoImageToVideoEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "7e290c67bff1f906cdab84523018573f6c9d4d7f"}, "widgets_values": [832, 480, 81, 0, 1, 1, true, false, false], "color": "#322", "bgcolor": "#533"}, {"id": 114, "type": "INTConstant", "pos": [2195, -83.40316009521484], "size": [210, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [249]}], "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "a6b867b63a29ca48ddb15c589e17a9f2d8530d57"}, "widgets_values": [6], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 118, "type": "CreateCFGScheduleFloatList", "pos": [2131.947998046875, -538.31103515625], "size": [298.3199157714844, 178], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 249}], "outputs": [{"label": "float_list", "name": "float_list", "type": "FLOAT", "links": [230, 250]}], "properties": {"Node name for S&R": "CreateCFGScheduleFloatList", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "7e290c67bff1f906cdab84523018573f6c9d4d7f"}, "widgets_values": [30, 1.8, 1.8, "linear", 0, 0.2]}, {"id": 135, "type": "WanVideoSampler", "pos": [2987.55126953125, -941.0934448242188], "size": [315, 975], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 240}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 241}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 248}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT", "link": 242}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS"}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS"}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS"}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS"}, {"label": "steps", "name": "steps", "type": 0, "widget": {"name": "steps"}}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "links": [245]}], "properties": {"Node name for S&R": "WanVideoSampler", "widget_ue_connectable": {}}, "widgets_values": [30, 6, 1, 8, "fixed", true, "unipc", 0, 1, false, "comfy", 3, -1]}, {"id": 134, "type": "WanVideoSampler", "pos": [2531.396240234375, -954.003173828125], "size": [315, 975], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 233}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 238}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 247}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS"}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS"}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS"}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS"}, {"label": "steps", "name": "steps", "type": 0, "widget": {"name": "steps"}}, {"label": "cfg", "name": "cfg", "type": 0, "widget": {"name": "cfg"}, "link": 250}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "links": [242]}], "properties": {"Node name for S&R": "WanVideoSampler", "widget_ue_connectable": {}}, "widgets_values": [30, 6, 6, 34, "fixed", true, "unipc", 0, 1, false, "comfy", 0, 3]}, {"id": 130, "type": "LoadImage", "pos": [875.904296875, -1337.25537109375], "size": [274.080078125, 314], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [212]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.44"}, "widgets_values": ["a3342a785af1f2f36ccd9b463e1e2cfcffc14ed8d22003f949d490117cabf2c2.jpg", "image", ""]}, {"id": 122, "type": "WanVideoModelLoader", "pos": [935.8306274414062, -476.4046936035156], "size": [477.4410095214844, 274], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS"}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 246}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL"}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [240]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e"}, "widgets_values": ["wan2.2_i2v_low_noise_14B_fp8_scaled.safetensors", "bf16", "fp8_e4m3fn_scaled", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 123, "type": "WanVideoModelLoader", "pos": [914.679443359375, -845.335205078125], "size": [477.4410095214844, 274], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 232}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 219}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL"}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [233]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "998a69cc0acbec503001b8b0ce0a5d5404420e1e"}, "widgets_values": ["wan2.2_i2v_high_noise_14B_fp8_scaled.safetensors", "bf16", "fp8_e4m3fn_scaled", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 128, "type": "VHS_VideoCombine", "pos": [3363.423583984375, -849.76318359375], "size": [698.6392211914062, 358], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 229}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8"}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideo2_2_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideo2_2_I2V_00001_uducz_1753779426.mp4", "workflow": "WanVideo2_2_I2V_00001.png", "fullpath": "/data/ComfyUI/personal/4802a607abc3bfe99da5613b806f8cc4/temp/WanVideo2_2_I2V_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "temp", "frame_rate": 16}}}}, {"id": 137, "type": "LoadImage", "pos": [416.7200012207031, -1504.077392578125], "size": [315, 314], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE"}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["pasted/9de6fca296db8ec3ae7720833949f78faff28936dbc4a5eb6c42aa5d6be0d517.png", "image", ""]}, {"id": 133, "type": "WanVideoBlockSwap", "pos": [383.86676025390625, -1107.4150390625], "size": [315, 154], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "links": [232]}], "properties": {"Node name for S&R": "WanVideoBlockSwap", "widget_ue_connectable": {}}, "widgets_values": [40, false, false, true, 0]}], "links": [[207, 116, 0, 117, 0, "WANVAE"], [208, 120, 0, 117, 2, "IMAGE"], [209, 120, 1, 117, 8, "INT"], [210, 120, 2, 117, 9, "INT"], [212, 130, 0, 120, 0, "IMAGE"], [213, 120, 0, 121, 0, "IMAGE"], [219, 110, 0, 123, 2, "WANVIDLORA"], [220, 119, 0, 124, 0, "WANTEXTENCODER"], [227, 116, 0, 127, 0, "WANVAE"], [229, 127, 0, 128, 0, "IMAGE"], [230, 118, 0, 132, 0, "*"], [232, 133, 0, 123, 1, "BLOCKSWAPARGS"], [233, 123, 0, 134, 0, "WANVIDEOMODEL"], [238, 124, 0, 134, 1, "WANVIDEOTEXTEMBEDS"], [240, 122, 0, 135, 0, "WANVIDEOMODEL"], [241, 124, 0, 135, 1, "WANVIDEOTEXTEMBEDS"], [242, 134, 0, 135, 3, "LATENT"], [245, 135, 0, 127, 1, "LATENT"], [246, 136, 0, 122, 2, "WANVIDLORA"], [247, 117, 0, 134, 2, "WANVIDIMAGE_EMBEDS"], [248, 117, 0, 135, 2, "WANVIDIMAGE_EMBEDS"], [249, 114, 0, 118, 0, "INT"], [250, 118, 0, 134, 18, "FLOAT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.6588450000000006, "offset": [-247.92161325831012, 1722.2072028974208]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "workspace_info": {"id": "cN6y_3lvDvPvEFPTSjQF_"}, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "comfy-core": "0.3.26"}}, "version": 0.4}