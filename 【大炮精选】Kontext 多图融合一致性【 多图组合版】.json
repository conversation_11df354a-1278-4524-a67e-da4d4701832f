{"id": "81ccfe3e-3540-49e3-88ae-4fde173b8c87", "revision": 0, "last_node_id": 190, "last_link_id": 292, "nodes": [{"id": 87, "type": "<PERSON>downNote", "pos": [-1016.8223876953125, 1611.8382568359375], "size": [376.663330078125, 279.697265625], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "Flux Kontext 提示词技巧", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["使用英文\n\n### 1. 基础修改\n- 简单直接：`\"Change the car color to red\"`\n- 保持风格：`\"Change to daytime while maintaining the same style of the painting\"`\n\n### 2. 风格转换\n**原则：**\n- 明确命名风格：`\"Transform to Bauhaus art style\"`\n- 描述特征：`\"Transform to oil painting with visible brushstrokes, thick paint texture\"`\n- 保留构图：`\"Change to Bauhaus style while maintaining the original composition\"`\n\n### 3. 角色一致性\n**框架：**\n- 具体描述：`\"The woman with short black hair\"`而非`\"她\"`\n- 保留特征：`\"while maintaining the same facial features, hairstyle, and expression\"`\n- 分步修改：先改背景，再改动作\n\n### 4. 文本编辑\n- 使用引号：`\"Replace 'joy' with 'BFL'\"`\n- 保持格式：`\"Replace text while maintaining the same font style\"`\n\n## 常见问题解决\n\n### 角色变化过大\n❌ 错误：`\"Transform the person into a Viking\"`\n✅ 正确：`\"Change the clothes to be a viking warrior while preserving facial features\"`\n\n### 构图位置改变\n❌ 错误：`\"Put him on a beach\"`\n✅ 正确：`\"Change the background to a beach while keeping the person in the exact same position, scale, and pose\"`\n\n### 风格应用不准确\n❌ 错误：`\"Make it a sketch\"`\n✅ 正确：`\"Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture\"`\n\n## 核心原则\n\n1. **具体明确** - 使用精确描述，避免模糊词汇\n2. **分步编辑** - 复杂修改分为多个简单步骤\n3. **明确保留** - 说明哪些要保持不变\n4. **动词选择** - 用\"更改\"、\"替换\"而非\"转换\"\n\n## 最佳实践模板\n\n**对象修改：**\n`\"Change [object] to [new state], keep [content to preserve] unchanged\"`\n\n**风格转换：**\n`\"Transform to [specific style], while maintaining [composition/character/other] unchanged\"`\n\n**背景替换：**\n`\"Change the background to [new background], keep the subject in the exact same position and pose\"`\n\n**文本编辑：**\n`\"Replace '[original text]' with '[new text]', maintain the same font style\"`\n\n> **记住：** 越具体越好，Kontext 擅长理解详细指令并保持一致性。"], "color": "#432", "bgcolor": "#653"}, {"id": 88, "type": "<PERSON>downNote", "pos": [-1015.7849731445312, 1287.783447265625], "size": [383.78765869140625, 273.2510070800781], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "Flux Kontext Prompt Techniques", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["### 1. Basic Modifications\n- Simple and direct: `\"Change the car color to red\"`\n- Maintain style: `\"Change to daytime while maintaining the same style of the painting\"`\n\n### 2. Style Transfer\n**Principles:**\n- Clearly name style: `\"Transform to Bauhaus art style\"`\n- Describe characteristics: `\"Transform to oil painting with visible brushstrokes, thick paint texture\"`\n- Preserve composition: `\"Change to Bauhaus style while maintaining the original composition\"`\n\n### 3. Character Consistency\n**Framework:**\n- Specific description: `\"The woman with short black hair\"` instead of \"she\"\n- Preserve features: `\"while maintaining the same facial features, hairstyle, and expression\"`\n- Step-by-step modifications: Change background first, then actions\n\n### 4. Text Editing\n- Use quotes: `\"Replace 'joy' with 'BFL'\"`\n- Maintain format: `\"Replace text while maintaining the same font style\"`\n\n## Common Problem Solutions\n\n### Character Changes Too Much\n❌ Wrong: `\"Transform the person into a Viking\"`\n✅ Correct: `\"Change the clothes to be a viking warrior while preserving facial features\"`\n\n### Composition Position Changes\n❌ Wrong: `\"Put him on a beach\"`\n✅ Correct: `\"Change the background to a beach while keeping the person in the exact same position, scale, and pose\"`\n\n### Style Application Inaccuracy\n❌ Wrong: `\"Make it a sketch\"`\n✅ Correct: `\"Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture\"`\n\n## Core Principles\n\n1. **Be Specific and Clear** - Use precise descriptions, avoid vague terms\n2. **Step-by-step Editing** - Break complex modifications into multiple simple steps\n3. **Explicit Preservation** - State what should remain unchanged\n4. **Verb Selection** - Use \"change\", \"replace\" rather than \"transform\"\n\n## Best Practice Templates\n\n**Object Modification:**\n`\"Change [object] to [new state], keep [content to preserve] unchanged\"`\n\n**Style Transfer:**\n`\"Transform to [specific style], while maintaining [composition/character/other] unchanged\"`\n\n**Background Replacement:**\n`\"Change the background to [new background], keep the subject in the exact same position and pose\"`\n\n**Text Editing:**\n`\"Replace '[original text]' with '[new text]', maintain the same font style\"`\n\n> **Remember:** The more specific, the better. Kontext excels at understanding detailed instructions and maintaining consistency. "], "color": "#432", "bgcolor": "#653"}, {"id": 86, "type": "<PERSON>downNote", "pos": [-1013.8328247070312, 926.7041625976562], "size": [380.9487609863281, 309.4496765136719], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "title": "About API Node", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["[About API Node](https://docs.comfy.org/tutorials/api-nodes/overview) | [关于 API 节点](https://docs.comfy.org/zh-CN/tutorials/api-nodes/overview)\n\nTo use the API, you must be in a secure network environment:\n\n- Allows access from `127.0.0.1` or `localhost`.\n\n- Use our API Node in website services starting with `https`\n\n- Make sure you can normally connect to our API services (some regions may need a proxy).\n\n- Make sure you are logged in in the settings and that your account still has enough credits to cover the consumption of API calls.\n\n- On non-whitelisted sites or local area networks (LANs), please try to [log in using an API Key](https://docs.comfy.org/interface/user#logging-in-with-an-api-key).\n\n---\n\n要使用API，你必须处于安全的网络环境中：\n\n- 允许从`127.0.0.1`或`localhost`访问。\n- 在带有 https 开头的服务中使用我们的 API Node\n- 确保你能够正常连接我们的API服务（某些地区可能需要代理）。\n- 确保你已在设置中登录，且你的账户仍有足够的积分来支付API调用的消耗。\n- 在非白名单站点或者局域网（LAN），请尝试[使用 API Key 来登录](https://docs.comfy.org/zh-CN/interface/user#%E4%BD%BF%E7%94%A8-api-key-%E8%BF%9B%E8%A1%8C%E7%99%BB%E5%BD%95)\n"], "color": "#432", "bgcolor": "#653"}, {"id": 95, "type": "easy imageRemBg", "pos": [426.**************, 1257.***********], "size": [344.*************, 477.*************], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 180}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [182, 184]}, {"label": "mask", "name": "mask", "type": "MASK"}], "properties": {"Node name for S&R": "easy imageRemBg", "cnr_id": "comfyui-easy-use", "ver": "1.3.0", "widget_ue_connectable": {}}, "widgets_values": ["RMBG-1.4", "Preview", "ComfyUI", false, "white", false], "color": "#3c98d1", "bgcolor": "#2884bd"}, {"id": 94, "type": "DF_Image_scale_to_side", "pos": [766.6158447265625, 1083.260009765625], "size": [220.2392120361328, 130], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 177}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [178, 179, 283]}], "properties": {"Node name for S&R": "DF_Image_scale_to_side", "cnr_id": "derfuu_comfyui_moddednodes", "ver": "d0905bed31249f2bd0814c67585cf4fe3c77c015", "widget_ue_connectable": {}}, "widgets_values": [1536, "Longest", "nearest-exact", "disabled"]}, {"id": 93, "type": "PreviewImage", "pos": [789.5372924804688, 1253.8206787109375], "size": [357.9523010253906, 258], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 178}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 98, "type": "ImageConcanate", "pos": [1011.1097412109375, 1090.5106201171875], "size": [210, 102], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 183}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 184}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [186]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["down", true]}, {"id": 100, "type": "ImageConcanate", "pos": [794.0108642578125, 1565.5389404296875], "size": [270, 102], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 186}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 285}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [188]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["right", true]}, {"id": 142, "type": "Note", "pos": [1232.8389892578125, 1577.055908203125], "size": [469.15106201171875, 106.16140747070312], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["核心工作区，设定好想要图像最终的尺寸，按照注释写好提示词"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 92, "type": "ImageConcanate", "pos": [432.9333190917969, 1094.817626953125], "size": [308.97930908203125, 111.12283325195312], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 173}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 182}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [177]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["right", true]}, {"id": 85, "type": "SaveImage", "pos": [1700.5625, 1087.3560791015625], "size": [433.8018798828125, 651.2936401367188], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 284}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"], "color": "#006691", "bgcolor": "rgba(24,24,27,.9)"}, {"id": 102, "type": "PreviewImage", "pos": [2149.295654296875, 1087.8759765625], "size": [634.4324951171875, 621.6918334960938], "flags": {}, "order": 47, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 188}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#006691", "bgcolor": "rgba(24,24,27,.9)"}, {"id": 151, "type": "Note", "pos": [761.7525024414062, 2540.642578125], "size": [469.15106201171875, 106.16140747070312], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["核心工作区，设定好想要图像最终的尺寸，按照注释写好提示词"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 154, "type": "SaveImage", "pos": [1229.47509765625, 2050.94287109375], "size": [433.8018798828125, 651.2936401367188], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 288}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"], "color": "#006691", "bgcolor": "rgba(24,24,27,.9)"}, {"id": 153, "type": "ImageConcanate", "pos": [425.7947082519531, 2054.4853515625], "size": [308.97930908203125, 111.12283325195312], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 256}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 261}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [249]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["right", true]}, {"id": 146, "type": "DF_Image_scale_to_side", "pos": [429.5304870605469, 2216.713623046875], "size": [313.56561279296875, 130], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 249}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [250, 253, 286]}], "properties": {"Node name for S&R": "DF_Image_scale_to_side", "cnr_id": "derfuu_comfyui_moddednodes", "ver": "d0905bed31249f2bd0814c67585cf4fe3c77c015", "widget_ue_connectable": {}}, "widgets_values": [1536, "Longest", "nearest-exact", "disabled"]}, {"id": 147, "type": "PreviewImage", "pos": [421.6475524902344, 2392.384033203125], "size": [316.808349609375, 258], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 250}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 152, "type": "Note", "pos": [416.7251281738281, 2695.79248046875], "size": [323.64215087890625, 88], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["这是抠出背景用的，如果不需要，可以ctrl+B忽略掉"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 148, "type": "ImageConcanate", "pos": [775.091064453125, 2702.953125], "size": [210, 102], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 251}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 260}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [254]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["down", true]}, {"id": 150, "type": "ImageConcanate", "pos": [900.8980712890625, 2703.629150390625], "size": [270, 102], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 254}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 287}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [259]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["right", true]}, {"id": 155, "type": "PreviewImage", "pos": [1681.702392578125, 2051.398193359375], "size": [689.62548828125, 685.9164428710938], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 259}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#006691", "bgcolor": "rgba(24,24,27,.9)"}, {"id": 143, "type": "Note", "pos": [438.40460205078125, 1789.038818359375], "size": [302.5684814453125, 88], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["这是抠出背景用的，如果不需要，可以ctrl+B忽略掉"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 170, "type": "Note", "pos": [-455.2617492675781, 1840.5084228515625], "size": [302.5684814453125, 88], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["人物图"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 171, "type": "Note", "pos": [38.324222564697266, 1824.7913818359375], "size": [302.5684814453125, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["产品图（尽量用好 描述的产品）"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 178, "type": "easy imageRemBg", "pos": [-78.51495361328125, 3006.981201171875], "size": [210, 362], "flags": {"collapsed": false}, "order": 26, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 281}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [282]}, {"label": "mask", "name": "mask", "type": "MASK"}], "properties": {"Node name for S&R": "easy imageRemBg", "cnr_id": "comfyui-easy-use", "ver": "1.3.0", "widget_ue_connectable": {}}, "widgets_values": ["RMBG-1.4", "Preview", "ComfyUI", false, "white", false], "color": "#3c98d1", "bgcolor": "#2884bd"}, {"id": 158, "type": "easy imageRemBg", "pos": [679.77783203125, 3010.0439453125], "size": [293.8857116699219, 365.13397216796875], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 262}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [271]}, {"label": "mask", "name": "mask", "type": "MASK"}], "properties": {"Node name for S&R": "easy imageRemBg", "cnr_id": "comfyui-easy-use", "ver": "1.3.0", "widget_ue_connectable": {}}, "widgets_values": ["RMBG-1.4", "Preview", "ComfyUI", false, "white", false], "color": "#3c98d1", "bgcolor": "#2884bd"}, {"id": 166, "type": "ImageConcanate", "pos": [699.77783203125, 3410.0439453125], "size": [308.97930908203125, 111.12283325195312], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 282}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 271}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [274, 275]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["down", true]}, {"id": 175, "type": "PreviewImage", "pos": [999.77783203125, 3010.0439453125], "size": [249.08143615722656, 511.0267639160156], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 274}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 176, "type": "ImageConcanate", "pos": [689.77783203125, 3550.0439453125], "size": [308.97930908203125, 111.12283325195312], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 275}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 276}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [278]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["right", true]}, {"id": 159, "type": "DF_Image_scale_to_side", "pos": [-6.48923921585083, 3845.64306640625], "size": [220.2392120361328, 130], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 278}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [264, 267, 280, 289]}], "properties": {"Node name for S&R": "DF_Image_scale_to_side", "cnr_id": "derfuu_comfyui_moddednodes", "ver": "d0905bed31249f2bd0814c67585cf4fe3c77c015", "widget_ue_connectable": {}}, "widgets_values": [2048, "Longest", "nearest-exact", "disabled"]}, {"id": 160, "type": "PreviewImage", "pos": [-16.489240646362305, 4035.64404296875], "size": [523.9874267578125, 483.3334045410156], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 264}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 164, "type": "Note", "pos": [1284.4122314453125, 3498.47900390625], "size": [441.2233581542969, 98.87594604492188], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["核心工作区，设定好想要图像最终的尺寸，按照注释写好提示词"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 163, "type": "ImageConcanate", "pos": [1281.1900634765625, 3641.77880859375], "size": [270, 102], "flags": {}, "order": 48, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 280}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 290}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [273]}], "properties": {"Node name for S&R": "ImageConcanate", "cnr_id": "comfyui-kjnodes", "ver": "07b804cb3ff3b3eb8d2c5fdadd62d0822bebe4e8", "widget_ue_connectable": {}}, "widgets_values": ["right", true]}, {"id": 167, "type": "SaveImage", "pos": [589.9883422851562, 3890.853515625], "size": [656.5653686523438, 678.8489379882812], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 291}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"], "color": "#006691", "bgcolor": "rgba(24,24,27,.9)"}, {"id": 180, "type": "Note", "pos": [-469.13592529296875, 3725.6513671875], "size": [302.5684814453125, 88], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["人物图"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 181, "type": "Note", "pos": [292.7982177734375, 3703.86279296875], "size": [302.5684814453125, 88], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["产品图（尽量用好 描述的产品）"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 179, "type": "Note", "pos": [-437.7865905761719, 4559.18798828125], "size": [302.5684814453125, 88], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {"widget_ue_connectable": {}}, "widgets_values": ["背景环境图"], "color": "#149463", "bgcolor": "#00804f"}, {"id": 168, "type": "PreviewImage", "pos": [1273.23583984375, 3903.802490234375], "size": [1193.7562255859375, 687.0096435546875], "flags": {}, "order": 50, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 273}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#006691", "bgcolor": "rgba(24,24,27,.9)"}, {"id": 182, "type": "Note Plus (mtb)", "pos": [-1173.0074462890625, 2791.8828125], "size": [545.32861328125, 207.91574096679688], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# <span style=\"font-family: 'Microsoft YaHei'; font-weight: bold; font-size: 40px; color: #FFAB3B;\">🏄‍资源说明🏄‍</span>\n## 本资源免费分享。需要VIP永久报错服务或者购买专属课也可以私我\n\n## 🧙需要的加微信🧙\n\n## VX号：paolaoshiAICG", "markdown", "", "one_dark"], "color": "#133372", "bgcolor": "#274786", "shape": 2}, {"id": 184, "type": "Note", "pos": [-1173.0074462890625, 3061.883056640625], "size": [553.5591430664062, 150.61676025390625], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["跑不动的可以考虑用我云端：\n\n2025满血云端一键运行，内含500+商业流！\nhttps://www.xiangongyun.com/image/detail/5b5aa403-8e9c-4a81-94a3-ce9a13febb47\n\n如果需要VIP云端也可联系我（可以在下面链接看我所有工作流展示效果）\nhttps://wxuyk58beb5.feishu.cn/docx/IZSadNNb0oqF9rxGw97cQQSgnUo\n"], "color": "#7a0059", "bgcolor": "#8e106d"}, {"id": 183, "type": "Note Plus (mtb)", "pos": [-1173.0074462890625, 1961.8826904296875], "size": [550, 810], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["# <span style=\"font-family: 'Microsoft YaHei'; font-weight: bold; font-size: 40px; color: #FFAB3B;\">🏄‍炮老师全套资料链接🏄‍</span>\n\n## <span style=\"font-family: 'Microsoft YaHei'; font-weight: bold; font-size: 26px; color: FFFFFF;\">👇整合包/入门课/工作流/VIP资源👇\n\n👉https://wxuyk58beb5.feishu.cn/docx/TJF1dpepNoHtsMxP5MfcNJhnnSf\n\n\n  <img src=\"https://s1.imagehub.cc/images/2025/01/26/0c1538ba59adc45adced814f12e44889.md.jpg\" alt=\"描述图片内容\" style=\"max-width: 100%; height: auto;\"></span>", "markdown", "", "one_dark"], "color": "#721330", "bgcolor": "#862744", "shape": 2}, {"id": 186, "type": "RH_ComfyFluxKontext", "pos": [1245.696533203125, 1083.911376953125], "size": [397.26312255859375, 377.3333435058594], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "input_image", "name": "input_image", "shape": 7, "type": "IMAGE", "link": 283}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [284, 285]}, {"label": "images_urls", "name": "images_urls", "type": "STRING", "links": []}, {"label": "log_info", "name": "log_info", "type": "STRING", "links": []}], "properties": {"Node name for S&R": "RH_ComfyFluxKontext"}, "widgets_values": ["女人手里拿着右边的产品在超市里面展示产品", "flux-kontext-max", "", "3:4", 1, 1235932115, "randomize", true, 3.5], "color": "#2a2563", "bgcolor": "#16114f"}, {"id": 187, "type": "RH_ComfyFluxKontext", "pos": [759.2359008789062, 2059.940185546875], "size": [397.26312255859375, 377.3333435058594], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "input_image", "name": "input_image", "shape": 7, "type": "IMAGE", "link": 286}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [287, 288]}, {"label": "images_urls", "name": "images_urls", "type": "STRING", "links": []}, {"label": "log_info", "name": "log_info", "type": "STRING", "links": []}], "properties": {"Node name for S&R": "RH_ComfyFluxKontext"}, "widgets_values": ["图中两个人拥抱在一起", "flux-kontext-max", "", "3:4", 1, 1379492798, "randomize", true, 3.5], "color": "#2a2563", "bgcolor": "#16114f"}, {"id": 91, "type": "LoadImage", "pos": [-97.29415130615234, 1107.5457763671875], "size": [494.8520812988281, 668.339111328125], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [180]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["pasted/7f596f0b6829e798e25de748cbcb85044e3ee06587ae0fb30b67e96a16d654ec.png", "image", ""], "color": "#1c44a0", "bgcolor": "#08308c"}, {"id": 156, "type": "LoadImage", "pos": [-538.15087890625, 2055.423095703125], "size": [428.2555236816406, 690.2247314453125], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [251, 256]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["pasted/c35e5423940495a5950fb2746fff9872269d017376c3b7202bb0a1a202e97007.png", "image", ""], "color": "#28b3cc", "bgcolor": "#149fb8"}, {"id": 144, "type": "LoadImage", "pos": [-92.88166809082031, 2052.432861328125], "size": [494.8520812988281, 668.339111328125], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [260, 261]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["pasted/f53ee8803e3b2a822dceee228a9eeedf4993f32f3babb63611118aa313525cfe.png", "image", ""], "color": "#1c44a0", "bgcolor": "#08308c"}, {"id": 169, "type": "LoadImage", "pos": [-518.286376953125, 2996.8193359375], "size": [428.2555236816406, 690.2247314453125], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [281]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["pasted/555b55401d5509e7649889af0a5533ddeb87f5552f877b7efd25cb017dee0b90.png", "image", ""], "color": "#28b3cc", "bgcolor": "#149fb8"}, {"id": 157, "type": "LoadImage", "pos": [169.77780151367188, 3000.0439453125], "size": [494.8520812988281, 668.339111328125], "flags": {}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [262]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["pasted/7f596f0b6829e798e25de748cbcb85044e3ee06587ae0fb30b67e96a16d654ec.png", "image", ""], "color": "#1c44a0", "bgcolor": "#08308c"}, {"id": 174, "type": "LoadImage", "pos": [-526.4889526367188, 3855.64306640625], "size": [494.8520812988281, 668.339111328125], "flags": {}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [276]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["pasted/0be5b3bb7df0ebf6e822ee536860615c7549be003a5b1cd6edaa856fb13c9ed3.png", "image"], "color": "#811d52", "bgcolor": "#6d093e"}, {"id": 188, "type": "RH_ComfyFluxKontext", "pos": [1274.6209716796875, 3034.540771484375], "size": [397.26312255859375, 377.3333435058594], "flags": {}, "order": 46, "mode": 0, "inputs": [{"label": "input_image", "name": "input_image", "shape": 7, "type": "IMAGE", "link": 289}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [290, 291]}, {"label": "images_urls", "name": "images_urls", "type": "STRING", "links": []}, {"label": "log_info", "name": "log_info", "type": "STRING", "links": []}], "properties": {"Node name for S&R": "RH_ComfyFluxKontext"}, "widgets_values": ["女人手里拿着产品坐在沙发上展示，保持沙发场景不变，产品尺寸跟女人头部大小一样", "flux-kontext-max", "", "3:4", 1, 591792752, "randomize", true, 3.5], "color": "#2a2563", "bgcolor": "#16114f"}, {"id": 84, "type": "LoadImage", "pos": [-542.5632934570312, 1110.5361328125], "size": [428.2555236816406, 690.2247314453125], "flags": {}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [173, 183]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}}, "widgets_values": ["pasted/6416974e951b91f525d09e42dabe66a54765e8e1e81210f2cb992ca08c459cbf.png", "image", ""], "color": "#28b3cc", "bgcolor": "#149fb8"}], "links": [[173, 84, 0, 92, 0, "IMAGE"], [177, 92, 0, 94, 0, "IMAGE"], [178, 94, 0, 93, 0, "IMAGE"], [180, 91, 0, 95, 0, "IMAGE"], [182, 95, 0, 92, 1, "IMAGE"], [183, 84, 0, 98, 0, "IMAGE"], [184, 95, 0, 98, 1, "IMAGE"], [186, 98, 0, 100, 0, "IMAGE"], [188, 100, 0, 102, 0, "IMAGE"], [249, 153, 0, 146, 0, "IMAGE"], [250, 146, 0, 147, 0, "IMAGE"], [251, 156, 0, 148, 0, "IMAGE"], [254, 148, 0, 150, 0, "IMAGE"], [256, 156, 0, 153, 0, "IMAGE"], [259, 150, 0, 155, 0, "IMAGE"], [260, 144, 0, 148, 1, "IMAGE"], [261, 144, 0, 153, 1, "IMAGE"], [262, 157, 0, 158, 0, "IMAGE"], [264, 159, 0, 160, 0, "IMAGE"], [271, 158, 0, 166, 1, "IMAGE"], [273, 163, 0, 168, 0, "IMAGE"], [274, 166, 0, 175, 0, "IMAGE"], [275, 166, 0, 176, 0, "IMAGE"], [276, 174, 0, 176, 1, "IMAGE"], [278, 176, 0, 159, 0, "IMAGE"], [280, 159, 0, 163, 0, "IMAGE"], [281, 169, 0, 178, 0, "IMAGE"], [282, 178, 0, 166, 0, "IMAGE"], [283, 94, 0, 186, 0, "IMAGE"], [284, 186, 0, 85, 0, "IMAGE"], [285, 186, 0, 100, 1, "IMAGE"], [286, 146, 0, 187, 0, "IMAGE"], [287, 187, 0, 150, 1, "IMAGE"], [288, 187, 0, 154, 0, "IMAGE"], [289, 159, 0, 188, 0, "IMAGE"], [290, 188, 0, 163, 1, "IMAGE"], [291, 188, 0, 167, 0, "IMAGE"]], "groups": [{"id": 1, "title": "【大炮精选】Kontext 多图融合一致性【 多图组合版】", "bounding": [-570.6441040039062, 740.4278564453125, 3328.3212890625, 159.0771942138672], "color": "#8AA", "font_size": 120, "flags": {}}, {"id": 2, "title": "电商带货 视频一致性 虚拟合影 漫画推文 背景融合等 更多玩法等你解锁", "bounding": [213.24246215820312, 601.40283203125, 2534.66259765625, 102.71128845214844], "color": "#3f789e", "font_size": 80, "flags": {}}, {"id": 3, "title": "电商带货适合", "bounding": [-552.5632934570312, 1009.6600341796875, 3371.713623046875, 920.4011840820312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "双人合影适合", "bounding": [-548.1509399414062, 1954.5474853515625, 2974.573974609375, 872.5303955078125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "三图拼合 适合场景人物产品融合", "bounding": [-536.4889526367188, 2923.21923828125, 3031.7021484375, 1721.5543212890625], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7247295000000004, "offset": [819.8857428421281, -877.8985053891647]}, "links_added_by_ue": [], "VHS_KeepIntermediate": true, "ue_links": [], "VHS_MetadataImage": true, "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.20.7", "VHS_latentpreview": false}, "version": 0.4}