{"id": "c1d0db20-f1e2-4556-b0d9-7c3e1a8cc0db", "revision": 0, "last_node_id": 22, "last_link_id": 27, "nodes": [{"id": 12, "type": "LoadImage", "pos": [-442.7200012207031, 268.72821044921875], "size": [316.4639892578125, 402.0880126953125], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [10]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "LoadImage"}, "widgets_values": ["7z7m5h7rr1rme0cq42tvdmtghr.png", "image"]}, {"id": 11, "type": "LoadImage", "pos": [-432.0842590332031, -100.25232696533203], "size": [270, 314], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [22]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "LoadImage"}, "widgets_values": ["Gsuh-hkbMAAR5Og.jfif", "image"]}, {"id": 15, "type": "ImageConcanate", "pos": [343.39984130859375, 22.368019104003906], "size": [289.3599853515625, 116.5199966430664], "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 13}, {"name": "image2", "type": "IMAGE", "link": 14}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [16]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "Node name for S&R": "ImageConcanate"}, "widgets_values": ["right", true]}, {"id": 10, "type": "ImageConcanate", "pos": [9.60003662109375, 39.20001220703125], "size": [270, 102], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": null}, {"name": "image2", "type": "IMAGE", "link": 10}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "Node name for S&R": "ImageConcanate"}, "widgets_values": ["up", true]}, {"id": 14, "type": "LoadImage", "pos": [350.7016296386719, 170.03515625], "size": [270, 314.0000305175781], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [14]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "LoadImage"}, "widgets_values": ["Gsut_Rxb0AAPJXO.jfif", "image"]}, {"id": 16, "type": "PreviewImage", "pos": [727.6945190429688, 11.711771011352539], "size": [409.85595703125, 696.9519653320312], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 16}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 22, "type": "PreviewImage", "pos": [720.84814453125, 765.8291625976562], "size": [395.5520935058594, 302.8479919433594], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 27}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 19, "type": "easy imageRemBg", "pos": [-19.671768188476562, 291.5092468261719], "size": [270, 350], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 22}], "outputs": [{"name": "image", "type": "IMAGE", "links": [25]}, {"name": "mask", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.0", "Node name for S&R": "easy imageRemBg"}, "widgets_values": ["RMBG-1.4", "Preview", "ComfyUI", false, "white", false]}, {"id": 21, "type": "DF_Image_scale_to_side", "pos": [304.60760498046875, 615.7891235351562], "size": [270, 130], "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 25}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [27]}], "properties": {"cnr_id": "derfuu_comfyui_moddednodes", "ver": "1.0.1", "Node name for S&R": "DF_Image_scale_to_side"}, "widgets_values": [2080, "<PERSON><PERSON><PERSON>", "lanc<PERSON>s", "disabled"]}], "links": [[10, 12, 0, 10, 1, "IMAGE"], [13, 10, 0, 15, 0, "IMAGE"], [14, 14, 0, 15, 1, "IMAGE"], [16, 15, 0, 16, 0, "IMAGE"], [22, 11, 0, 19, 0, "IMAGE"], [25, 19, 0, 21, 0, "IMAGE"], [27, 21, 0, 22, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.8390545288824064, "offset": [683.9357590605847, 198.1196953011646]}, "frontendVersion": "1.20.6"}, "version": 0.4}