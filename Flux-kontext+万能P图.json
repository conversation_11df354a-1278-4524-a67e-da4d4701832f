{"id": "7cbcec68-7fa6-47bb-a38a-da689949a001", "revision": 0, "last_node_id": 239, "last_link_id": 368, "nodes": [{"id": 6, "type": "CLIPTextEncode", "pos": [30, 570], "size": [400, 220], "flags": {"collapsed": true}, "order": 23, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 59}, {"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 295}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [237, 294]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {"text": true}}, "widgets_values": ["Using this elegant style, create a portrait of a swan wearing a pearl tiara and lace collar, maintaining the same refined quality and soft color tones.", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 8, "type": "VAEDecode", "pos": [410, 430], "size": [190, 46], "flags": {"collapsed": true}, "order": 43, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 52}, {"name": "vae", "type": "VAE", "link": 61}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [240, 299]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 31, "type": "K<PERSON><PERSON><PERSON>", "pos": [670, 240], "size": [320, 474], "flags": {"collapsed": false}, "order": 42, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 351}, {"name": "positive", "type": "CONDITIONING", "link": 57}, {"name": "negative", "type": "CONDITIONING", "link": 238}, {"name": "latent_image", "type": "LATENT", "link": 355}], "outputs": [{"name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": [50341946073256, "randomize", 20, 1, "euler", "simple", 1]}, {"id": 35, "type": "FluxGuidance", "pos": [530, 430], "size": [210, 58], "flags": {"collapsed": true}, "order": 41, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 292}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxGuidance", "widget_ue_connectable": {}}, "widgets_values": [2.5]}, {"id": 37, "type": "UNETLoader", "pos": [-380, 360], "size": [340, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [341, 352]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "UNETLoader", "models": [{"name": "flux1-dev-kontext_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["F.1 Kontext dev_fp8.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "DualCLIPLoader", "pos": [-380, 490], "size": [340, 130], "flags": {"collapsed": false}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "CLIP", "type": "CLIP", "links": [59]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "DualCLIPLoader", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "VAELoader", "pos": [-380, 670], "size": [340, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "VAE", "type": "VAE", "links": [61, 223]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "FluxKontextImageScale", "pos": [420, 560], "size": [240, 30], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 251}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [222, 289, 316]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 124, "type": "VAEEncode", "pos": [400, 610], "size": [240, 50], "flags": {"collapsed": true}, "order": 38, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 222}, {"name": "vae", "type": "VAE", "link": 223}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [293]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEEncode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 135, "type": "ConditioningZeroOut", "pos": [540, 500], "size": [210, 26], "flags": {"collapsed": true}, "order": 29, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 237}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [238]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "ConditioningZeroOut", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 146, "type": "ImageStitch", "pos": [30, 280], "size": [340, 150], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "图像1", "name": "image1", "type": "IMAGE", "link": 298}, {"label": "图像2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 250}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [251]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "ImageStitch", "widget_ue_connectable": {}}, "widgets_values": ["right", true, 0, "white"]}, {"id": 147, "type": "LoadImageOutput", "pos": [-830, 240], "size": [400, 490], "flags": {}, "order": 3, "mode": 2, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [250]}, {"name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImageOutput", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_temp_pfzzl_00004_.png [output]", false, "refresh", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 177, "type": "ReferenceLatent", "pos": [400, 500], "size": [210, 50], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [{"name": "conditioning", "type": "CONDITIONING", "link": 294}, {"name": "latent", "shape": 7, "type": "LATENT", "link": 293}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [292]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ReferenceLatent", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 185, "type": "<PERSON>downNote", "pos": [-1680, -510], "size": [480, 500], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "title": "孤海COMFYUI资源中心", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["### [1.Flux Kontext万能P图模型与工作流下载](https://pan.baidu.com/s/1PaTrJ0b1XxpSWsmn64T0zw?pwd=ghai)\n\n\n### [2.孤海ComfyUI会员资料](https://g07twv432kc.feishu.cn/docx/EvNpdSaM8ouDt1x0HWBcAjwen0e)\n\n### [3.F.1 kontext万能P图云端镜像](https://www.xiangongyun.com/image/detail/684df438-0109-4c23-bcfb-b6c91f067375?r=8NA4MK)"], "color": "#233", "bgcolor": "#355"}, {"id": 187, "type": "<PERSON>downNote", "pos": [-2270, -510], "size": [567.1599731445312, 902.1049194335938], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "title": "Flux Kontext 提示词技巧（中文版）", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["## Flux Kontext 提示词技巧（中文版）\n\n\n## 1. 基础修改\n- 简单直接：\"将汽车颜色改为红色\"\n- 保持风格：\"改为白天场景，同时保持画面原有风格\"\n### 2. 风格转换\n**原则：**\n\n- 明确命名风格：\"转换为包豪斯艺术风格\"\n- 描述特征：\"转换为油画风格，要求保留清晰笔触和厚重颜料质感\"\n- 保留构图：\"转换为包豪斯风格，同时保持原始构图不变\"\n### 3. 角色一致性\n​**框架：**\n\n- 具体描述：使用\"黑色短发的女性\"而非\"她\"\n- 保留特征：\"同时保持相同面部特征、发型和表情\"\n- 分步修改：先修改背景，再调整动作\n### 4. 文本编辑\n- 使用引号：\"将'joy'替换为'BFL'\"\n- 保持格式：\"替换文本内容，同时保持字体样式不变\"\n\n## 常见问题解决\n### 角色变化过大\n❌ 错误：\"将人物转变成维京人\"\n\n✅ 正确：\"更换服装为维京战士造型，同时保留面部特征不变\"\n\n### 构图位置改变\n❌ 错误：\"把他放到海滩上\"\n\n✅ 正确：\"将背景更换为海滩，同时确保人物位置、比例和姿势完全不变\"\n\n### 风格应用不准确\n❌ 错误：\"改成素描风格\"\n\n✅ 正确：\"转换为铅笔素描，要求呈现自然石墨线条、交叉排线效果和可见纸张纹理\"\n\n### 核心原则\n​1.**具体明确**​ - 使用精确描述，避免模糊词汇\n\n​2.**分步编辑**​ - 复杂修改拆解为多个简单步骤\n\n​3.**明确保留**​ - 说明哪些要保留不变\n\n​4.**动词选择**​ - 优先使用\"更改\"、\"替换\"，而非\"转换\"\n\n## 最佳实践模板\n​对象修改：​​\n\"将[对象]更改为[新状态]，保持[需保留内容]不变\"\n\n​风格转换：​​\n\"转换为[特定风格]，同时保持[构图/角色/其他要素]不变\"\n\n​背景替换：​​\n\"将背景更改为[新背景]，确保主体位置和姿势完全一致\"\n\n​文本编辑：​​\n\"将'[原文本]'替换为'[新文本]'，同时维持相同字体样式\"\n\n> **记住：** 越具体越好，Kontext 擅长理解详细指令并保持一致性。"], "color": "#232", "bgcolor": "#353"}, {"id": 189, "type": "DeepTranslatorTextNode", "pos": [40, 470], "size": [400, 340], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [{"name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 296}], "outputs": [{"label": "文本", "name": "text", "type": "STRING", "links": [295]}], "properties": {"cnr_id": "comfyui_custom_nodes_alekpet", "ver": "5fc22da44a04b8d5f75e91d0d9bb9f961e5ab721", "Node name for S&R": "DeepTranslatorTextNode", "widget_ue_connectable": {"text": true}}, "widgets_values": ["chinese (simplified)", "english", false, "", "", "GoogleTranslator", "", "proxy_hide", "authorization_hide", [false, true], [false, true], [false, true]]}, {"id": 190, "type": "TextInput_", "pos": [-830, -520], "size": [400, 360], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "字符串", "name": "STRING", "type": "STRING", "links": [296]}], "title": "提示词（支持中文）", "properties": {"cnr_id": "comfyui-mixlab-nodes", "ver": "b2bb1876def6330fccf1e03cc69d2166cae7bedb", "Node name for S&R": "TextInput_", "widget_ue_connectable": {}}, "widgets_values": ["把背景换成高档餐厅，同时保持人物面部特征完全不变", [false, true]], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 192, "type": "FluxKontextImageScale", "pos": [40, 240], "size": [270, 30], "flags": {"collapsed": true}, "order": 32, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 339}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [298, 358]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 193, "type": "Image Comparer (rgthree)", "pos": [520, -560], "size": [500, 690], "flags": {"collapsed": false}, "order": 45, "mode": 0, "inputs": [{"dir": 3, "label": "图像A", "name": "image_a", "type": "IMAGE", "link": 299}, {"dir": 3, "label": "图像B", "name": "image_b", "type": "IMAGE", "link": 316}], "outputs": [], "properties": {"cnr_id": "rgthree-comfy", "ver": "b775441a4c5ae2853d4ff6b97a137f7f7d11e597", "comparer_mode": "Slide", "widget_ue_connectable": {}}, "widgets_values": [[{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_eihzx_00041_.png&type=temp&subfolder=&rand=0.906190595334202"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_eihzx_00042_.png&type=temp&subfolder=&rand=0.372468945271492"}]]}, {"id": 200, "type": "<PERSON>downNote", "pos": [-2850, -510], "size": [553.6025390625, 901.1517944335938], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "title": "Flux Kontext 提示词技巧（英文版）", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["\n## Flux Kontext 提示词技巧（英文版）\n\n\n### 1. 基础修改\n- 简单直接：`\"Change the car color to red\"`\n- 保持风格：`\"Change to daytime while maintaining the same style of the painting\"`\n\n### 2. 风格转换\n**原则：**\n- 明确命名风格：`\"Transform to Bauhaus art style\"`\n- 描述特征：`\"Transform to oil painting with visible brushstrokes, thick paint texture\"`\n- 保留构图：`\"Change to Bauhaus style while maintaining the original composition\"`\n\n### 3. 角色一致性\n**框架：**\n- 具体描述：`\"The woman with short black hair\"`而非`\"她\"`\n- 保留特征：`\"while maintaining the same facial features, hairstyle, and expression\"`\n- 分步修改：先改背景，再改动作\n\n### 4. 文本编辑\n- 使用引号：`\"Replace 'joy' with 'BFL'\"`\n- 保持格式：`\"Replace text while maintaining the same font style\"`\n\n## 常见问题解决\n\n### 角色变化过大\n❌ 错误：`\"Transform the person into a Viking\"`\n✅ 正确：`\"Change the clothes to be a viking warrior while preserving facial features\"`\n\n### 构图位置改变\n❌ 错误：`\"Put him on a beach\"`\n✅ 正确：`\"Change the background to a beach while keeping the person in the exact same position, scale, and pose\"`\n\n### 风格应用不准确\n❌ 错误：`\"Make it a sketch\"`\n✅ 正确：`\"Convert to pencil sketch with natural graphite lines, cross-hatching, and visible paper texture\"`\n\n## 核心原则\n\n1. **具体明确** - 使用精确描述，避免模糊词汇\n2. **分步编辑** - 复杂修改分为多个简单步骤\n3. **明确保留** - 说明哪些要保持不变\n4. **动词选择** - 用\"更改\"、\"替换\"而非\"转换\"\n\n## 最佳实践模板\n\n**对象修改：**\n`\"Change [object] to [new state], keep [content to preserve] unchanged\"`\n\n**风格转换：**\n`\"Transform to [specific style], while maintaining [composition/character/other] unchanged\"`\n\n**背景替换：**\n`\"Change the background to [new background], keep the subject in the exact same position and pose\"`\n\n**文本编辑：**\n`\"Replace '[original text]' with '[new text]', maintain the same font style\"`\n\n> **记住：** 越具体越好，Kontext 擅长理解详细指令并保持一致性。"], "color": "#432", "bgcolor": "#653"}, {"id": 201, "type": "Advertisement", "pos": [-2850, -760], "size": [1677.737548828125, 172.07235717773438], "flags": {"showTitle": false, "pinned": true}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "title": "使用说明与资料下载", "properties": {"borderRadius": 0, "backgroundColor": "transparent", "padding": 0, "fitMode": "contain", "flipH": false, "flipV": false, "autoplay": true, "loop": true, "mediaSource": "input/image_display/资源.png", "volume": 0, "widget_ue_connectable": {}}, "color": "transparent", "bgcolor": "transparent", "mediaType": "image", "tempFilePath": "input/image_display/资源.png"}, {"id": 205, "type": "Advertisement", "pos": [-840, -800], "size": [410, 190], "flags": {"showTitle": false, "pinned": true}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {"borderRadius": 0, "backgroundColor": "transparent", "padding": 0, "fitMode": "contain", "flipH": false, "flipV": false, "autoplay": true, "loop": true, "mediaSource": "input/image_display/logo.png", "volume": 0, "widget_ue_connectable": {}}, "color": "transparent", "bgcolor": "transparent", "mediaType": "image", "tempFilePath": "input/image_display/logo.png"}, {"id": 206, "type": "EmptySD3LatentImage", "pos": [430, 670], "size": [270, 106], "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 367}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 368}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [355]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "EmptySD3LatentImage", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [1024, 1024, 1]}, {"id": 211, "type": "Image Blend by Mask", "pos": [530, 250], "size": [270, 98], "flags": {"collapsed": true}, "order": 31, "mode": 0, "inputs": [{"name": "image_a", "type": "IMAGE", "link": 335}, {"name": "image_b", "type": "IMAGE", "link": 325}, {"label": "遮罩图像", "name": "mask", "type": "IMAGE", "link": 332}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [339]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1cd8d304eda256c412b8589ce1f00be3c61cf9ec", "Node name for S&R": "Image Blend by Mask", "widget_ue_connectable": {}}, "widgets_values": [1]}, {"id": 212, "type": "MaskToImage", "pos": [390, 360], "size": [140, 30], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [{"name": "mask", "type": "MASK", "link": 344}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [330]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "MaskToImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 214, "type": "LayerUtility: ColorImage", "pos": [430, 300], "size": [270, 106], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [{"name": "width", "type": "INT", "widget": {"name": "width"}, "link": 322}, {"name": "height", "type": "INT", "widget": {"name": "height"}, "link": 323}], "outputs": [{"label": "图像", "name": "image", "type": "IMAGE", "links": [325]}], "properties": {"cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f", "Node name for S&R": "LayerUtility: ColorImage", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [512, 512, "#c000ff"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 215, "type": "GetImageSize", "pos": [380, 250], "size": [140, 124], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 336}], "outputs": [{"label": "宽度", "name": "width", "type": "INT", "links": [322, 328]}, {"label": "高度", "name": "height", "type": "INT", "links": [323, 329]}, {"label": "批量大小", "name": "batch_size", "type": "INT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "GetImageSize", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 218, "type": "孤海图像缩放按像素", "pos": [480, 360], "size": [210, 386], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"name": "图像", "type": "IMAGE", "link": 330}, {"name": "遮罩", "shape": 7, "type": "MASK", "link": null}, {"name": "宽度", "type": "INT", "widget": {"name": "宽度"}, "link": 328}, {"name": "高度", "type": "INT", "widget": {"name": "高度"}, "link": 329}], "outputs": [{"name": "图像", "type": "IMAGE", "links": [332]}, {"name": "宽度", "type": "INT", "links": null}, {"name": "高度", "type": "INT", "links": null}, {"name": "遮罩", "type": "MASK", "links": null}, {"name": "扩展遮罩", "type": "MASK", "links": null}, {"name": "布尔", "type": "BOOLEAN", "links": null}], "properties": {"Node name for S&R": "孤海图像缩放按像素", "widget_ue_connectable": {"宽度": true, "高度": true}}, "widgets_values": ["自定义宽高", 512, 512, 1024, "<PERSON><PERSON><PERSON><PERSON>", "拉伸", "总是", false, "#ffffff", false]}, {"id": 221, "type": "LoadImageOutput", "pos": [-400, -550], "size": [390, 680], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [335, 336]}, {"name": "MASK", "type": "MASK", "links": [343]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "LoadImageOutput", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_temp_pfzzl_00004_.png [output]", false, "refresh", "image"], "color": "#432", "bgcolor": "#653"}, {"id": 223, "type": "FluxForwardOverrider", "pos": [-380, 260], "size": [176.03768920898438, 26], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 341}], "outputs": [{"name": "model", "type": "MODEL", "links": [348]}], "properties": {"Node name for S&R": "FluxForwardOverrider", "cnr_id": "comfyui_patches_ll", "ver": "4a9ea6e5d408768d5c2d666781ef756880eea0d0", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 225, "type": "Mask Fill Holes", "pos": [380, 300], "size": [140, 26], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [{"label": "遮罩", "name": "masks", "type": "MASK", "link": 343}], "outputs": [{"label": "遮罩", "name": "MASKS", "type": "MASK", "links": [344]}], "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "1cd8d304eda256c412b8589ce1f00be3c61cf9ec", "Node name for S&R": "Mask Fill Holes", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 227, "type": "ApplyTeaCachePatch", "pos": [-210, 260], "size": [270, 106], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 348}], "outputs": [{"name": "model", "type": "MODEL", "links": [353]}], "properties": {"Node name for S&R": "ApplyTeaCachePatch", "cnr_id": "comfyui_patches_ll", "ver": "4a9ea6e5d408768d5c2d666781ef756880eea0d0", "widget_ue_connectable": {}}, "widgets_values": [0.4000000000000001]}, {"id": 228, "type": "easy ifElse", "pos": [530, 300], "size": [230, 78], "flags": {"collapsed": true}, "order": 28, "mode": 0, "inputs": [{"label": "真时", "name": "on_true", "type": "*", "link": 353}, {"label": "假时", "name": "on_false", "type": "*", "link": 352}, {"label": "布尔", "name": "boolean", "type": "BOOLEAN", "widget": {"name": "boolean"}, "link": 354}], "outputs": [{"name": "*", "type": "*", "links": [351]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "44f067632352c449263e11f0d9b708c9fd30e989", "Node name for S&R": "easy ifElse", "widget_ue_connectable": {"boolean": true}}, "widgets_values": [false]}, {"id": 229, "type": "easy boolean", "pos": [-390, 240], "size": [360, 70], "flags": {"collapsed": false, "pinned": true}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "布尔", "name": "boolean", "type": "BOOLEAN", "links": [354]}], "title": "模型加速开关", "properties": {"cnr_id": "comfyui-easy-use", "ver": "44f067632352c449263e11f0d9b708c9fd30e989", "Node name for S&R": "easy boolean", "widget_ue_connectable": {}}, "widgets_values": [true], "color": "#223", "bgcolor": "#335"}, {"id": 231, "type": "GetImageSize", "pos": [150, 240], "size": [140, 124], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [{"label": "图像", "name": "image", "type": "IMAGE", "link": 358}], "outputs": [{"label": "宽度", "name": "width", "type": "INT", "links": [361]}, {"label": "高度", "name": "height", "type": "INT", "links": [362]}, {"label": "批量大小", "name": "batch_size", "type": "INT", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.42", "Node name for S&R": "GetImageSize", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 232, "type": "SetNode", "pos": [300, 240], "size": [210, 60], "flags": {"collapsed": true}, "order": 36, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 361}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_W-in", "properties": {"previousName": "W-in", "widget_ue_connectable": {}}, "widgets_values": ["W-in"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 233, "type": "SetNode", "pos": [300, 240], "size": [210, 58], "flags": {"collapsed": true}, "order": 37, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 362}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_H-in", "properties": {"previousName": "H-in", "widget_ue_connectable": {}}, "widgets_values": ["H-in"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 234, "type": "GetNode", "pos": [-560, 0], "size": [210, 60], "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [363]}], "title": "Get_W-in", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["W-in"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 235, "type": "GetNode", "pos": [-550, 0], "size": [210, 58], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [364]}], "title": "Get_H-in", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["H-in"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 236, "type": "SetNode", "pos": [-560, 0], "size": [210, 60], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 365}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_W-out", "properties": {"previousName": "W-out", "widget_ue_connectable": {}}, "widgets_values": ["W-out"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 237, "type": "SetNode", "pos": [-560, 0], "size": [210, 58], "flags": {"collapsed": true}, "order": 27, "mode": 0, "inputs": [{"name": "INT", "type": "INT", "link": 366}], "outputs": [{"label": "输出", "name": "*", "type": "*", "links": null}], "title": "Set_H-out", "properties": {"previousName": "H-out", "widget_ue_connectable": {}}, "widgets_values": ["H-out"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 238, "type": "GetNode", "pos": [520, 610], "size": [210, 60], "flags": {"collapsed": true}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [367]}], "title": "Get_W-out", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["W-out"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 239, "type": "GetNode", "pos": [520, 610], "size": [210, 58], "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"name": "INT", "type": "INT", "links": [368]}], "title": "Get_H-out", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["H-out"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 173, "type": "PreviewImage", "pos": [30, 470], "size": [340, 260], "flags": {"collapsed": false}, "order": 39, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 289}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 230, "type": "孤海_kontext生图比例", "pos": [-820, -30], "size": [360, 150], "flags": {"collapsed": false}, "order": 20, "mode": 0, "inputs": [{"name": "宽度", "type": "INT", "widget": {"name": "宽度"}, "link": 363}, {"name": "高度", "type": "INT", "widget": {"name": "高度"}, "link": 364}], "outputs": [{"name": "宽度", "type": "INT", "links": [365]}, {"name": "高度", "type": "INT", "links": [366]}], "properties": {"Node name for S&R": "孤海_kontext生图比例", "widget_ue_connectable": {}}, "widgets_values": [1024, 1024, "原始比例"], "color": "#432", "bgcolor": "#653"}, {"id": 136, "type": "SaveImage", "pos": [20, -540], "size": [470, 670], "flags": {"collapsed": false}, "order": 44, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 240}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}], "links": [[52, 31, 0, 8, 0, "LATENT"], [57, 35, 0, 31, 1, "CONDITIONING"], [59, 38, 0, 6, 0, "CLIP"], [61, 39, 0, 8, 1, "VAE"], [222, 42, 0, 124, 0, "IMAGE"], [223, 39, 0, 124, 1, "VAE"], [237, 6, 0, 135, 0, "CONDITIONING"], [238, 135, 0, 31, 2, "CONDITIONING"], [240, 8, 0, 136, 0, "IMAGE"], [250, 147, 0, 146, 1, "IMAGE"], [251, 146, 0, 42, 0, "IMAGE"], [289, 42, 0, 173, 0, "IMAGE"], [292, 177, 0, 35, 0, "CONDITIONING"], [293, 124, 0, 177, 1, "LATENT"], [294, 6, 0, 177, 0, "CONDITIONING"], [295, 189, 0, 6, 1, "STRING"], [296, 190, 0, 189, 0, "STRING"], [298, 192, 0, 146, 0, "IMAGE"], [299, 8, 0, 193, 0, "IMAGE"], [316, 42, 0, 193, 1, "IMAGE"], [322, 215, 0, 214, 0, "INT"], [323, 215, 1, 214, 1, "INT"], [325, 214, 0, 211, 1, "IMAGE"], [328, 215, 0, 218, 2, "INT"], [329, 215, 1, 218, 3, "INT"], [330, 212, 0, 218, 0, "IMAGE"], [332, 218, 0, 211, 2, "IMAGE"], [335, 221, 0, 211, 0, "IMAGE"], [336, 221, 0, 215, 0, "IMAGE"], [339, 211, 0, 192, 0, "IMAGE"], [341, 37, 0, 223, 0, "MODEL"], [343, 221, 1, 225, 0, "MASK"], [344, 225, 0, 212, 0, "MASK"], [348, 223, 0, 227, 0, "MODEL"], [351, 228, 0, 31, 0, "MODEL"], [352, 37, 0, 228, 1, "*"], [353, 227, 0, 228, 0, "*"], [354, 229, 0, 228, 2, "BOOLEAN"], [355, 206, 0, 31, 3, "LATENT"], [358, 192, 0, 231, 0, "IMAGE"], [361, 231, 0, 232, 0, "*"], [362, 231, 1, 233, 0, "*"], [363, 234, 0, 230, 0, "INT"], [364, 235, 0, 230, 1, "INT"], [365, 230, 0, 236, 0, "*"], [366, 230, 1, 237, 0, "*"], [367, 238, 0, 206, 0, "INT"], [368, 239, 0, 206, 1, "INT"]], "floatingLinks": [{"id": 2, "origin_id": 189, "origin_slot": 0, "target_id": -1, "target_slot": -1, "type": "*", "parentId": 1}], "groups": [{"id": 1, "title": "Load models", "bounding": [-410, 160, 410, 590], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Conditioning", "bounding": [10, 160, 1020, 590], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "上传原图", "bounding": [-410, -630, 410, 770], "color": "#b58b2a", "font_size": 30, "flags": {}}, {"id": 8, "title": "上传参考图", "bounding": [-840, 160, 420, 590], "color": "#3f789e", "font_size": 30, "flags": {}}, {"id": 9, "title": "保存图像", "bounding": [10, -630, 490, 770], "color": "#3f789e", "font_size": 30, "flags": {}}, {"id": 10, "title": "图像对比", "bounding": [510, -630, 520, 770], "color": "#3f789e", "font_size": 22, "flags": {}}, {"id": 11, "title": "FLUX-Kontext 万能P图工作流", "bounding": [-410, -750, 1440, 110], "color": "#3f789e", "font_size": 80, "flags": {}}, {"id": 12, "title": "提示词", "bounding": [-840, -610, 420, 470], "color": "#3f789e", "font_size": 22, "flags": {}}, {"id": 13, "title": "输出比例", "bounding": [-840, -130, 420, 270], "color": "#b58b2a", "font_size": 30, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7972024500000008, "offset": [863.9670163548708, 811.4391127263622]}, "frontendVersion": "1.23.4", "groupNodes": {}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "reroutes": [{"id": 1, "pos": [150, -200], "linkIds": [], "floating": {"slotType": "output"}}, {"id": 3, "pos": [340, 530], "linkIds": [316]}, {"id": 4, "pos": [330, 410], "linkIds": [299]}, {"id": 5, "parentId": 6, "pos": [360, 510], "linkIds": [296]}, {"id": 6, "pos": [280, -490], "linkIds": [296]}], "ue_links": [], "links_added_by_ue": [], "linkExtensions": [{"id": 296, "parentId": 5}, {"id": 299, "parentId": 4}, {"id": 316, "parentId": 3}]}, "version": 0.4}