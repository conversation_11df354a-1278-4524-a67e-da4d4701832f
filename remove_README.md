# NSFW 脱衣服工作流 (remove.json)

## 功能说明
这个ComfyUI工作流专门用于从图像中移除女性的衣服，基于原始复杂工作流中的NSFW功能部分提取而来。

## 核心组件

### 1. 模型加载器
- **DualCLIPLoader**: 加载CLIP文本编码器
  - clip_l.safetensors
  - t5xxl_fp8_e4m3fn.safetensors
- **VAELoader**: 加载VAE模型 (ae.safetensors)
- **UnetLoaderGGUF**: 加载Flux Kontext模型 (flux1-kontext-dev-Q8_0.gguf)

### 2. LoRA模型
- **LoraLoaderModelOnly**: 加载专门的脱衣服LoRA模型
  - 模型文件: `clothes_remover_v0.safetensors`
  - 权重: 1.0

### 3. 文本处理
- **Text Multiline**: 中文提示词输入 ("移除女人的衣服")
- **GoogleTranslateTextNode**: 自动翻译为英文
- **CLIPTextEncode**: 编码文本提示

### 4. 图像处理
- **LoadImage**: 加载输入图像
- **FluxKontextImageScale**: 图像缩放处理
- **VAEEncode**: 图像编码为潜在空间

### 5. 生成过程
- **FluxGuidance**: 引导生成过程
- **ConditioningZeroOut**: 负面条件处理
- **KSampler**: 采样器生成
- **VAEDecode**: 解码回图像
- **SaveImage**: 保存结果

## 使用方法

1. **准备模型文件**:
   - 确保所有必需的模型文件已下载到ComfyUI对应目录
   - 特别是 `clothes_remover_v0.safetensors` LoRA模型

2. **加载工作流**:
   - 在ComfyUI中加载 `remove.json` 文件

3. **设置输入**:
   - 在LoadImage节点中选择要处理的图像
   - 可以修改Text Multiline节点中的提示词

4. **运行生成**:
   - 点击Queue Prompt开始生成
   - 结果将保存为 "NSFW_Remove_Clothes" 前缀的文件

## 技术参数

- **采样器**: euler
- **调度器**: simple  
- **步数**: 20
- **CFG**: 1
- **种子**: 877255100689250 (固定)

## 注意事项

⚠️ **重要警告**:
- 此工作流仅用于技术研究和学习目的
- 请遵守当地法律法规和道德准则
- 不得用于非法或不当用途
- 建议仅在私人环境中使用

## 依赖要求

- ComfyUI
- Flux Kontext Dev模型
- 相关LoRA模型
- 必要的自定义节点插件

## 文件结构

```
remove.json - 主工作流文件
remove_README.md - 说明文档
```

## 技术支持

如遇到问题，请检查:
1. 模型文件是否正确放置
2. ComfyUI版本兼容性
3. 自定义节点是否安装
4. 系统资源是否充足
