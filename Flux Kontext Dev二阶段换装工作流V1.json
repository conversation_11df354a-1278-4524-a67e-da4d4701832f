{"last_link_id": 386, "nodes": [{"outputs": [{"shape": 6, "name": "STRING", "label": "STRING", "type": "STRING", "localized_name": "STRING"}], "color": "#223", "widgets_values": ["Remove women's clothes", "Remove women's clothes"], "inputs": [{"name": "text", "link": 318, "label": "text", "type": "STRING"}], "flags": {"pinned": true}, "type": "ShowText|pysssss", "mode": 0, "bgcolor": "#335", "size": [384.5976867675781, 200.37255859375], "pos": [3388.808837890625, 417.0299987792969], "id": 201, "properties": {"cnr_id": "comfyui-custom-scripts", "ver": "aac13aa7ce35b07d43633c3bbe654a38c00d74f5", "widget_ue_connectable": {}, "Node name for S&R": "ShowText|pysssss", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 19}, {"outputs": [{"name": "CLIP", "links": [319, 354], "label": "CLIP", "type": "CLIP", "localized_name": "CLIP"}], "color": "#322", "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"], "inputs": [], "flags": {"pinned": true}, "type": "DualCLIPLoader", "mode": 0, "bgcolor": "#533", "size": [337.76861572265625, 130], "pos": [2476.27587890625, 814.2345581054688], "id": 202, "properties": {"cnr_id": "comfy-core", "models": [{"name": "clip_l.safetensors", "directory": "text_encoders", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors"}], "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "DualCLIPLoader"}, "order": 0}, {"outputs": [{"name": "VAE", "links": [324, 328, 334, 358], "label": "VAE", "type": "VAE", "localized_name": "VAE"}], "color": "#322", "widgets_values": ["ae.safetensors"], "inputs": [], "flags": {"pinned": true}, "type": "VAELoader", "mode": 0, "bgcolor": "#533", "size": [337.76861572265625, 58], "pos": [2392.010009765625, 1015.8859252929688], "id": 203, "properties": {"cnr_id": "comfy-core", "models": [{"name": "ae.safetensors", "directory": "vae", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors"}], "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "VAELoader"}, "order": 1}, {"outputs": [{"name": "CONDITIONING", "slot_index": 0, "links": [321, 335], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#232", "widgets_values": ["Using this elegant style, create a portrait of a swan wearing a pearl tiara and lace collar, maintaining the same refined quality and soft color tones."], "inputs": [{"name": "clip", "link": 319, "label": "clip", "type": "CLIP", "localized_name": "clip"}, {"widget": {"name": "text"}, "name": "text", "link": 320, "label": "text", "type": "STRING"}], "flags": {}, "type": "CLIPTextEncode", "mode": 0, "bgcolor": "#353", "size": [385.7064514160156, 93], "pos": [3377.654296875, 986.7821655273438], "id": 204, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {"text": true}, "Node name for S&R": "CLIPTextEncode"}, "order": 20}, {"outputs": [{"name": "CONDITIONING", "links": [341], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "conditioning", "link": 321, "label": "conditioning", "type": "CONDITIONING", "localized_name": "conditioning"}], "flags": {"collapsed": false}, "type": "ConditioningZeroOut", "mode": 0, "bgcolor": "#335", "size": [240, 26], "pos": [3659.54150390625, 858.6183471679688], "id": 205, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "ConditioningZeroOut", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 27}, {"outputs": [], "color": "#223", "widgets_values": ["T8star-Aix"], "inputs": [{"name": "images", "link": 322, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {"pinned": true}, "type": "SaveImage", "mode": 4, "bgcolor": "#335", "size": [407.2988586425781, 299.3293762207031], "pos": [4873.939453125, 290.1796569824219], "id": 206, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 36}, {"outputs": [{"name": "IMAGE", "slot_index": 0, "links": [322, 332, 362], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "samples", "link": 323, "label": "samples", "type": "LATENT", "localized_name": "samples"}, {"name": "vae", "link": 324, "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {"pinned": true, "collapsed": false}, "type": "VAEDecode", "mode": 0, "bgcolor": "#335", "size": [190, 46], "pos": [4022.74462890625, 600.2445678710938], "id": 207, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "VAEDecode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 34}, {"outputs": [], "color": "#223", "widgets_values": ["ComfyUI"], "inputs": [{"name": "images", "link": 325, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {"pinned": true}, "type": "SaveImage", "mode": 4, "bgcolor": "#335", "size": [315, 270], "pos": [5593.63818359375, 754.6080932617188], "id": 208, "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 50}, {"outputs": [{"name": "STRING", "links": [366], "label": "STRING", "type": "STRING", "localized_name": "STRING"}], "color": "#223", "widgets_values": ["移除女人的衣服"], "inputs": [], "flags": {"pinned": true}, "type": "Text Multiline", "mode": 0, "bgcolor": "#335", "size": [375.3995361328125, 206.6213836669922], "pos": [2488.807861328125, 357.5431823730469], "id": 209, "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "ea935d1044ae5a26efa54ebeb18fe9020af49a45", "widget_ue_connectable": {}, "Node name for S&R": "Text Multiline", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 2}, {"outputs": [{"name": "CONDITIONING", "links": [350], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "conditioning", "link": 326, "label": "conditioning", "type": "CONDITIONING", "localized_name": "conditioning"}], "flags": {"collapsed": false}, "type": "ConditioningZeroOut", "mode": 0, "bgcolor": "#335", "size": [240, 26], "pos": [4144.25927734375, 1596.02978515625], "id": 210, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "ConditioningZeroOut", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 28}, {"outputs": [{"name": "IMAGE", "slot_index": 0, "links": [338, 353, 384], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "samples", "link": 327, "label": "samples", "type": "LATENT", "localized_name": "samples"}, {"name": "vae", "link": 328, "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {"pinned": true, "collapsed": false}, "type": "VAEDecode", "mode": 0, "bgcolor": "#335", "size": [190, 46], "pos": [5066.02734375, 1519.359619140625], "id": 211, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "VAEDecode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 46}, {"outputs": [{"name": "MODEL", "links": [368], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": [7, 7], "inputs": [{"name": "model", "link": 329, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "MZ_Flux1PartialLoad_Patch", "mode": 4, "bgcolor": "#335", "size": [330.2437438964844, 82], "pos": [3202.20361328125, 1172.1201171875], "id": 213, "properties": {"cnr_id": "ComfyUI-FluxExt-MZ", "ver": "00e4b70dea2b1d9b3f99a4fd267687bc69bdda98", "widget_ue_connectable": {}, "Node name for S&R": "MZ_Flux1PartialLoad_Patch", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 29}, {"outputs": [{"name": "MODEL", "links": [], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#322", "widgets_values": ["flux1-dev-kontext_fp8_scaled.safetensors", "default"], "inputs": [], "flags": {}, "type": "UNETLoader", "mode": 4, "bgcolor": "#533", "size": [337.76861572265625, 82], "pos": [2198.161376953125, 1202.169189453125], "id": 214, "properties": {"cnr_id": "comfy-core", "models": [{"name": "flux1-dev-kontext_fp8_scaled.safetensors", "directory": "diffusion_models", "url": "https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors"}], "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "UNETLoader"}, "order": 3}, {"outputs": [{"name": "images", "links": [361], "label": "images", "type": "IMAGE", "localized_name": "images"}], "color": "#223", "widgets_values": [2, "down", true, null], "inputs": [{"name": "image_1", "link": 331, "label": "image_1", "type": "IMAGE", "localized_name": "image_1"}, {"name": "image_2", "link": 332, "label": "image_2", "type": "IMAGE", "localized_name": "image_2"}], "flags": {}, "type": "ImageConcatMulti", "mode": 0, "bgcolor": "#335", "size": [315, 150], "pos": [4451.111328125, 569.5211791992188], "id": 216, "properties": {"cnr_id": "comfyui-kjnodes", "ver": "f7eb33abc80a2aded1b46dff0dd14d07856a7d50", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 37}, {"outputs": [{"name": "LATENT", "links": [336, 342], "label": "LATENT", "type": "LATENT", "localized_name": "LATENT"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "pixels", "link": 333, "label": "pixels", "type": "IMAGE", "localized_name": "pixels"}, {"name": "vae", "link": 334, "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {"collapsed": false}, "type": "VAEEncode", "mode": 0, "bgcolor": "#335", "size": [240, 50], "pos": [3381.999267578125, 857.6554565429688], "id": 217, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "VAEEncode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 25}, {"outputs": [{"name": "CONDITIONING", "links": [337], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "conditioning", "link": 335, "label": "conditioning", "type": "CONDITIONING", "localized_name": "conditioning"}, {"shape": 7, "name": "latent", "link": 336, "label": "latent", "type": "LATENT", "localized_name": "latent"}], "flags": {}, "type": "ReferenceLatent", "mode": 0, "bgcolor": "#335", "size": [211.60000610351562, 46], "pos": [3409.921142578125, 728.6439819335938], "id": 218, "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "Node name for S&R": "ReferenceLatent", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 30}, {"outputs": [{"name": "images", "links": [325], "label": "images", "type": "IMAGE", "localized_name": "images"}], "color": "#223", "widgets_values": [2, 25, 854551255, "randomize", "lanczos4"], "inputs": [{"name": "images", "link": 338, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "PMRF", "mode": 4, "bgcolor": "#335", "size": [315, 178], "pos": [5205.22021484375, 772.7824096679688], "id": 223, "properties": {"cnr_id": "ComfyUI-PMRF", "ver": "e105c042161785b6fbbae840e70817896c1eebf4", "widget_ue_connectable": {}, "Node name for S&R": "PMRF", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 47}, {"outputs": [{"name": "STRING", "links": [330], "label": "STRING", "type": "STRING", "localized_name": "STRING"}], "color": "#223", "widgets_values": ["让裸体的女人穿上图片中的衣服，且人物脸部，表情，动作保持不变"], "inputs": [], "flags": {"pinned": true}, "type": "Text Multiline", "mode": 0, "bgcolor": "#335", "size": [375.3995361328125, 206.6213836669922], "pos": [3000.587890625, 1501.92431640625], "id": 229, "properties": {"cnr_id": "was-node-suite-comfyui", "ver": "ea935d1044ae5a26efa54ebeb18fe9020af49a45", "widget_ue_connectable": {}, "Node name for S&R": "Text Multiline", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 4}, {"outputs": [{"name": "CONDITIONING", "links": [364], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "conditioning", "link": 344, "label": "conditioning", "type": "CONDITIONING", "localized_name": "conditioning"}, {"shape": 7, "name": "latent", "link": 345, "label": "latent", "type": "LATENT", "localized_name": "latent"}], "flags": {}, "type": "ReferenceLatent", "mode": 0, "bgcolor": "#335", "size": [211.60000610351562, 46], "pos": [3885.989990234375, 1483.637939453125], "id": 230, "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "Node name for S&R": "ReferenceLatent", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 43}, {"outputs": [{"name": "IMAGE", "links": [347, 363], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "MASK", "label": "MASK", "type": "MASK", "localized_name": "MASK"}], "color": "#223", "widgets_values": [200, 200, 200, 200, 40], "inputs": [{"name": "image", "link": 376, "label": "image", "type": "IMAGE", "localized_name": "image"}], "flags": {}, "type": "ImagePadForOutpaint", "mode": 0, "bgcolor": "#335", "size": [270, 174], "pos": [4139.25146484375, 2218.72314453125], "id": 232, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "ImagePadForOutpaint", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 17}, {"outputs": [{"name": "CONDITIONING", "slot_index": 0, "links": [326, 344], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#232", "widgets_values": ["Using this elegant style, create a portrait of a swan wearing a pearl tiara and lace collar, maintaining the same refined quality and soft color tones."], "inputs": [{"name": "clip", "link": 354, "label": "clip", "type": "CLIP", "localized_name": "clip"}, {"widget": {"name": "text"}, "name": "text", "link": 355, "label": "text", "type": "STRING"}], "flags": {}, "type": "CLIPTextEncode", "mode": 0, "bgcolor": "#353", "size": [385.7064514160156, 93], "pos": [3524.49609375, 1696.64501953125], "id": 241, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {"text": true}, "Node name for S&R": "CLIPTextEncode"}, "order": 21}, {"outputs": [{"name": "LATENT", "links": [345], "label": "LATENT", "type": "LATENT", "localized_name": "LATENT"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "pixels", "link": 357, "label": "pixels", "type": "IMAGE", "localized_name": "pixels"}, {"name": "vae", "link": 358, "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {"collapsed": false}, "type": "VAEEncode", "mode": 0, "bgcolor": "#335", "size": [240, 50], "pos": [4609.34130859375, 1542.946533203125], "id": 243, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "VAEEncode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 41}, {"outputs": [{"name": "IMAGE", "links": [333], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "image", "link": 359, "label": "image", "type": "IMAGE", "localized_name": "image"}], "flags": {"collapsed": false}, "type": "FluxKontextImageScale", "mode": 0, "bgcolor": "#335", "size": [314.1544189453125, 28.620174407958984], "pos": [2963.603271484375, 861.8490600585938], "id": 244, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "FluxKontextImageScale", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 16}, {"outputs": [{"name": "MODEL", "links": [329], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": ["clothes_remover_v0.safetensors", 1], "inputs": [{"name": "model", "link": 360, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "LoraLoaderModelOnly", "mode": 0, "bgcolor": "#335", "size": [270, 82], "pos": [2785.21484375, 1169.59521484375], "id": 245, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "LoraLoaderModelOnly", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 23}, {"outputs": [], "color": "#223", "widgets_values": ["T8star-Aix"], "inputs": [{"name": "images", "link": 361, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "SaveImage", "mode": 4, "bgcolor": "#335", "size": [407.2988586425781, 299.3293762207031], "pos": [3836.400634765625, 46.285118103027344], "id": 246, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 39}, {"outputs": [{"name": "MODEL", "links": [369, 373], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": ["flux1-kontext-dev-Q8_0.gguf"], "inputs": [], "flags": {}, "type": "UnetLoaderGGUF", "mode": 0, "bgcolor": "#335", "size": [270, 58], "pos": [2121.93505859375, 1434.637451171875], "id": 247, "properties": {"cnr_id": "comfyui-gguf", "ver": "b3ec875a68d94b758914fd48d30571d953bb7a54", "widget_ue_connectable": {}, "Node name for S&R": "UnetLoaderGGUF", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 5}, {"outputs": [{"name": "IMAGE", "links": [343, 357, 365], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#223", "widgets_values": ["right", true, 0, "white"], "inputs": [{"name": "image1", "link": 362, "label": "image1", "type": "IMAGE", "localized_name": "image1"}, {"shape": 7, "name": "image2", "link": 363, "label": "image2", "type": "IMAGE", "localized_name": "image2"}], "flags": {}, "type": "ImageStitch", "mode": 0, "bgcolor": "#335", "size": [270, 150], "pos": [3995.90966796875, 1720.977294921875], "id": 248, "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "widget_ue_connectable": {}, "Node name for S&R": "ImageStitch", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 38}, {"outputs": [{"name": "IMAGE", "links": [], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "image", "link": 365, "label": "image", "type": "IMAGE", "localized_name": "image"}], "flags": {"collapsed": false}, "type": "FluxKontextImageScale", "mode": 0, "bgcolor": "#335", "size": [523.490234375, 137.1434783935547], "pos": [4673.87255859375, 1801.12890625], "id": 250, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "FluxKontextImageScale", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 42}, {"outputs": [{"name": "text", "links": [318, 320], "label": "text", "type": "STRING", "localized_name": "text"}], "color": "#223", "widgets_values": ["zh-cn", "en", false, "Manual Trasnlate", ""], "inputs": [{"widget": {"name": "text"}, "name": "text", "link": 366, "label": "text", "type": "STRING"}], "flags": {}, "type": "GoogleTranslateTextNode", "mode": 0, "bgcolor": "#335", "size": [400, 200], "pos": [2933.392822265625, 402.2062683105469], "id": 251, "properties": {"cnr_id": "comfyui_custom_nodes_alekpet", "ver": "a77f4034d9a038efd80d17eedcfcba50248a13a8", "widget_ue_connectable": {}, "Node name for S&R": "GoogleTranslateTextNode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 11}, {"outputs": [{"name": "MODEL", "links": [339], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": ["HyperSD-Accelerator-FLUX-PAseerV2.safetensors", 1], "inputs": [{"name": "model", "link": 368, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "LoraLoaderModelOnly", "mode": 4, "bgcolor": "#335", "size": [270, 82], "pos": [3586.958740234375, 1192.1201171875], "id": 253, "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "Node name for S&R": "LoraLoaderModelOnly", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 31}, {"outputs": [{"name": "CONDITIONING", "slot_index": 0, "links": [340], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#223", "widgets_values": [10], "inputs": [{"name": "conditioning", "link": 337, "label": "conditioning", "type": "CONDITIONING", "localized_name": "conditioning"}], "flags": {"collapsed": false}, "type": "FluxGuidance", "mode": 0, "bgcolor": "#335", "size": [240, 58], "pos": [3702.************, 718.5477905273438], "id": 222, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "FluxGuidance", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 32}, {"outputs": [{"name": "model", "links": [360], "label": "model", "type": "MODEL", "localized_name": "model"}], "color": "#322", "shape": 1, "widgets_values": ["flux", 0.20000000000000004, 0.10000000000000002, 2], "inputs": [{"name": "model", "link": 369, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "<PERSON><PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#533", "size": [315.3089294433594, 178], "pos": [2489.************, 1298.010986328125], "id": 254, "properties": {"ver": "9a785013fecad3a56b0089b3369ec35ce378e81b", "widget_ue_connectable": {}, "aux_id": "Zehong-Ma/ComfyUI-MagCache", "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>"}, "order": 13}, {"outputs": [{"name": "CONDITIONING", "slot_index": 0, "links": [349], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#223", "widgets_values": [1], "inputs": [{"name": "conditioning", "link": 364, "label": "conditioning", "type": "CONDITIONING", "localized_name": "conditioning"}], "flags": {"collapsed": false}, "type": "FluxGuidance", "mode": 0, "bgcolor": "#335", "size": [240, 58], "pos": [4362.12646484375, 1388.9449462890625], "id": 249, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "FluxGuidance", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 44}, {"outputs": [{"name": "output", "label": "output", "type": "*", "localized_name": "output"}], "color": "#223", "widgets_values": ["Let the naked woman wear the clothes in the picture, and keep the characters' faces, expressions and movements unchanged"], "inputs": [{"shape": 7, "name": "anything", "link": 375, "label": "anything", "type": "*", "localized_name": "anything"}], "flags": {}, "type": "easy showAnything", "mode": 0, "bgcolor": "#335", "size": [210, 76], "pos": [3648.************, 1972.6990966796875], "id": 255, "properties": {"Node name for S&R": "easy showAnything", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 22}, {"outputs": [{"name": "text", "links": [355, 375], "label": "text", "type": "STRING", "localized_name": "text"}], "color": "#223", "widgets_values": ["zh-cn", "en", false, "Manual Trasnlate", ""], "inputs": [{"widget": {"name": "text"}, "name": "text", "link": 330, "label": "text", "type": "STRING"}], "flags": {}, "type": "GoogleTranslateTextNode", "mode": 0, "bgcolor": "#335", "size": [400, 200], "pos": [3074.406982421875, 1948.6009521484375], "id": 215, "properties": {"cnr_id": "comfyui_custom_nodes_alekpet", "ver": "a77f4034d9a038efd80d17eedcfcba50248a13a8", "widget_ue_connectable": {}, "Node name for S&R": "GoogleTranslateTextNode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 12}, {"outputs": [{"name": "LATENT", "slot_index": 0, "links": [327], "label": "LATENT", "type": "LATENT", "localized_name": "LATENT"}], "color": "#223", "widgets_values": [95567542424, "fixed", 20, 1, "euler", "simple", 1], "inputs": [{"name": "model", "link": 374, "label": "model", "type": "MODEL", "localized_name": "model"}, {"name": "positive", "link": 349, "label": "positive", "type": "CONDITIONING", "localized_name": "positive"}, {"name": "negative", "link": 350, "label": "negative", "type": "CONDITIONING", "localized_name": "negative"}, {"name": "latent_image", "link": 351, "label": "latent_image", "type": "LATENT", "localized_name": "latent_image"}], "flags": {}, "type": "K<PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#335", "size": [318.5348205566406, 486], "pos": [4816.8779296875, 947.2260131835938], "id": 234, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "K<PERSON><PERSON><PERSON>", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 45}, {"outputs": [], "color": "#223", "widgets_values": [], "inputs": [{"name": "images", "link": 343, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "PreviewImage", "mode": 4, "bgcolor": "#335", "size": [140, 246], "pos": [4724.529296875, 2479.4912109375], "id": 228, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "PreviewImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 40}, {"outputs": [], "color": "#223", "widgets_values": [], "inputs": [{"name": "images", "link": 347, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "PreviewImage", "mode": 4, "bgcolor": "#335", "size": [140, 246], "pos": [4135.5615234375, 2505.35400390625], "id": 233, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "PreviewImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 26}, {"outputs": [{"name": "MODEL", "links": [374], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": ["HyperSD-Accelerator-FLUX-PAseerV2.safetensors", 1], "inputs": [{"name": "model", "link": 373, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "LoraLoaderModelOnly", "mode": 4, "bgcolor": "#335", "size": [270, 82], "pos": [3397.8203125, 1373.6015625], "id": 235, "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "widget_ue_connectable": {}, "Node name for S&R": "LoraLoaderModelOnly", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 14}, {"outputs": [{"name": "LATENT", "slot_index": 0, "links": [323, 351, 377], "label": "LATENT", "type": "LATENT", "localized_name": "LATENT"}], "color": "#223", "widgets_values": [877255100689250, "fixed", 20, 1, "euler", "simple", 1], "inputs": [{"name": "model", "link": 339, "label": "model", "type": "MODEL", "localized_name": "model"}, {"name": "positive", "link": 340, "label": "positive", "type": "CONDITIONING", "localized_name": "positive"}, {"name": "negative", "link": 341, "label": "negative", "type": "CONDITIONING", "localized_name": "negative"}, {"name": "latent_image", "link": 342, "label": "latent_image", "type": "LATENT", "localized_name": "latent_image"}], "flags": {}, "type": "K<PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#335", "size": [329.6587829589844, 474.0001220703125], "pos": [4431.8115234375, -139.7356414794922], "id": 225, "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "K<PERSON><PERSON><PERSON>", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 33}, {"outputs": [], "color": "#223", "widgets_values": ["latents/ComfyUI"], "inputs": [{"name": "samples", "link": 377, "label": "samples", "type": "LATENT", "localized_name": "samples"}], "flags": {}, "type": "SaveLatent", "mode": 0, "bgcolor": "#335", "size": [315, 58], "pos": [4925.0009765625, -88.90827178955078], "id": 258, "properties": {"Node name for S&R": "SaveLatent", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 35}, {"outputs": [{"name": "VAE", "links": [380], "label": "VAE", "type": "VAE", "localized_name": "VAE"}], "color": "#322", "widgets_values": ["ae.safetensors"], "inputs": [], "flags": {}, "type": "VAELoader", "mode": 4, "bgcolor": "#533", "size": [337.76861572265625, 58], "pos": [1906.240234375, 2647.4033203125], "id": 259, "properties": {"cnr_id": "comfy-core", "models": [{"name": "ae.safetensors", "directory": "vae", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors"}], "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "VAELoader"}, "order": 6}, {"outputs": [{"name": "LATENT", "links": [379], "label": "LATENT", "type": "LATENT", "localized_name": "LATENT"}], "color": "#223", "widgets_values": [null], "inputs": [], "flags": {}, "type": "LoadLatent", "mode": 4, "bgcolor": "#335", "size": [315, 58], "pos": [1923.203857421875, 2537.1376953125], "id": 260, "properties": {"Node name for S&R": "LoadLatent", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 7}, {"outputs": [], "color": "#223", "widgets_values": ["ComfyUI"], "inputs": [{"name": "images", "link": 378, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "SaveImage", "mode": 4, "bgcolor": "#335", "size": [315, 58], "pos": [2624.376708984375, 2585.202880859375], "id": 261, "properties": {"Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 24}, {"mode": 4, "outputs": [{"name": "IMAGE", "slot_index": 0, "links": [378], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "bgcolor": "#335", "size": [190, 46], "color": "#223", "pos": [2341.341552734375, 2590.219970703125], "inputs": [{"name": "samples", "link": 379, "label": "samples", "type": "LATENT", "localized_name": "samples"}, {"name": "vae", "link": 380, "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {"collapsed": false}, "id": 262, "type": "VAEDecode", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "widget_ue_connectable": {}, "Node name for S&R": "VAEDecode", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 15}, {"outputs": [], "color": "#223", "widgets_values": ["ComfyUI", ""], "inputs": [{"name": "images", "link": 353, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "SaveImage", "mode": 0, "bgcolor": "#335", "size": [862.7942504882812, 665.824462890625], "pos": [5356.64453125, 1202.42431640625], "id": 239, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 48}, {"outputs": [{"name": "IMAGE", "links": [331, 359, 381], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "MASK", "label": "MASK", "type": "MASK", "localized_name": "MASK"}], "color": "#322", "widgets_values": ["3c3d8a7e1f7091c1df3e1329d45b4b9016ef565281bad44b5d924875fa8f7a22.jpg", "image", ""], "inputs": [], "flags": {}, "type": "LoadImage", "mode": 0, "bgcolor": "#533", "size": [340, 350], "pos": [2025.489990234375, -71.45658111572266], "id": 212, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "LoadImage"}, "order": 8}, {"outputs": [{"name": "IMAGE", "links": [376, 382], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "MASK", "label": "MASK", "type": "MASK", "localized_name": "MASK"}], "color": "#322", "widgets_values": ["d65a5715120720235adcbc66428d201e4a764817b1ab43b5f7ed368d944bd3bb.webp", "image", ""], "inputs": [], "flags": {}, "type": "LoadImage", "mode": 0, "bgcolor": "#533", "size": [340, 350], "pos": [3713.997802734375, 2282.251220703125], "id": 256, "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "widget_ue_connectable": {}, "Node name for S&R": "LoadImage"}, "order": 9}, {"outputs": [{"name": "IMAGE", "links": [385], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#223", "widgets_values": ["down", true], "inputs": [{"name": "image1", "link": 381, "label": "image1", "type": "IMAGE", "localized_name": "image1"}, {"name": "image2", "link": 382, "label": "image2", "type": "IMAGE", "localized_name": "image2"}], "flags": {}, "type": "ImageConcanate", "mode": 0, "bgcolor": "#335", "size": [315, 102], "pos": [5628.9208984375, 2123.06884765625], "id": 263, "properties": {"Node name for S&R": "ImageConcanate", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 18}, {"outputs": [{"name": "IMAGE", "links": [386], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#223", "widgets_values": ["right", true], "inputs": [{"name": "image1", "link": 385, "label": "image1", "type": "IMAGE", "localized_name": "image1"}, {"name": "image2", "link": 384, "label": "image2", "type": "IMAGE", "localized_name": "image2"}], "flags": {}, "type": "ImageConcanate", "mode": 0, "bgcolor": "#335", "size": [315, 102], "pos": [5647.71923828125, 2312.4638671875], "id": 265, "properties": {"Node name for S&R": "ImageConcanate", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 49}, {"outputs": [], "color": "#432", "widgets_values": ["加一点外补似乎会让衣服更好换，，"], "inputs": [], "flags": {}, "type": "Note", "mode": 0, "bgcolor": "#653", "size": [331.05810546875, 191.084716796875], "pos": [4462.59423828125, 2223.675048828125], "id": 267, "properties": {}, "order": 10}, {"outputs": [], "color": "#223", "widgets_values": ["ComfyUI", ""], "inputs": [{"name": "images", "link": 386, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "SaveImage", "mode": 0, "bgcolor": "#335", "size": [820.429931640625, 981.851806640625], "pos": [6286.49169921875, 1962.4169921875], "id": 266, "properties": {"Node name for S&R": "SaveImage", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "order": 51}], "extra": {"VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "groupNodes": {}, "node_versions": {"comfy-core": "0.3.42", "ComfyUI-MagCache": "d53d144466f4abbbe105b6045730df8141ede8b4"}, "ds": {"offset": [-5505.750113644292, -2253.7874302448886], "scale": 1.167184107045013}}, "groups": [{"color": "#3f789e", "font_size": 240, "flags": {}, "id": 7, "title": "B站、Youtube：T8star-Aix", "bounding": [1973.380126953125, -618.5014038085938, 4168, 309]}, {"color": "#3f789e", "font_size": 24, "flags": {}, "id": 8, "title": "本地解码", "bounding": [1912.518798828125, 2455.82666015625, 1033.34814453125, 251.86563110351562]}], "links": [[318, 251, 0, 201, 0, "STRING"], [319, 202, 0, 204, 0, "CLIP"], [320, 251, 0, 204, 1, "STRING"], [321, 204, 0, 205, 0, "CONDITIONING"], [322, 207, 0, 206, 0, "IMAGE"], [323, 225, 0, 207, 0, "LATENT"], [324, 203, 0, 207, 1, "VAE"], [325, 223, 0, 208, 0, "IMAGE"], [326, 241, 0, 210, 0, "CONDITIONING"], [327, 234, 0, 211, 0, "LATENT"], [328, 203, 0, 211, 1, "VAE"], [329, 245, 0, 213, 0, "MODEL"], [330, 229, 0, 215, 0, "STRING"], [331, 212, 0, 216, 0, "IMAGE"], [332, 207, 0, 216, 1, "IMAGE"], [333, 244, 0, 217, 0, "IMAGE"], [334, 203, 0, 217, 1, "VAE"], [335, 204, 0, 218, 0, "CONDITIONING"], [336, 217, 0, 218, 1, "LATENT"], [337, 218, 0, 222, 0, "CONDITIONING"], [338, 211, 0, 223, 0, "IMAGE"], [339, 253, 0, 225, 0, "MODEL"], [340, 222, 0, 225, 1, "CONDITIONING"], [341, 205, 0, 225, 2, "CONDITIONING"], [342, 217, 0, 225, 3, "LATENT"], [343, 248, 0, 228, 0, "IMAGE"], [344, 241, 0, 230, 0, "CONDITIONING"], [345, 243, 0, 230, 1, "LATENT"], [347, 232, 0, 233, 0, "IMAGE"], [349, 249, 0, 234, 1, "CONDITIONING"], [350, 210, 0, 234, 2, "CONDITIONING"], [351, 225, 0, 234, 3, "LATENT"], [353, 211, 0, 239, 0, "IMAGE"], [354, 202, 0, 241, 0, "CLIP"], [355, 215, 0, 241, 1, "STRING"], [357, 248, 0, 243, 0, "IMAGE"], [358, 203, 0, 243, 1, "VAE"], [359, 212, 0, 244, 0, "IMAGE"], [360, 254, 0, 245, 0, "MODEL"], [361, 216, 0, 246, 0, "IMAGE"], [362, 207, 0, 248, 0, "IMAGE"], [363, 232, 0, 248, 1, "IMAGE"], [364, 230, 0, 249, 0, "CONDITIONING"], [365, 248, 0, 250, 0, "IMAGE"], [366, 209, 0, 251, 0, "STRING"], [368, 213, 0, 253, 0, "MODEL"], [369, 247, 0, 254, 0, "MODEL"], [373, 247, 0, 235, 0, "MODEL"], [374, 235, 0, 234, 0, "MODEL"], [375, 215, 0, 255, 0, "*"], [376, 256, 0, 232, 0, "IMAGE"], [377, 225, 0, 258, 0, "LATENT"], [378, 262, 0, 261, 0, "IMAGE"], [379, 260, 0, 262, 0, "LATENT"], [380, 259, 0, 262, 1, "VAE"], [381, 212, 0, 263, 0, "IMAGE"], [382, 256, 0, 263, 1, "IMAGE"], [384, 211, 0, 265, 1, "IMAGE"], [385, 263, 0, 265, 0, "IMAGE"], [386, 265, 0, 266, 0, "IMAGE"]], "id": "96c5bc7f-602a-499b-91b6-662e34a933d6", "config": {}, "version": 0.4, "last_node_id": 267, "revision": 0}