{"id": "96995b8f-85c5-47af-b3cf-7b6a24675694", "revision": 0, "last_node_id": 212, "last_link_id": 305, "nodes": [{"id": 110, "type": "CLIPLoader", "pos": [-260, 380], "size": [350, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [300, 301]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.34", "models": [{"name": "umt5_xxl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors", "directory": "text_encoders"}]}, "widgets_values": ["umt5_xxl_fp16.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 7, "type": "CLIPTextEncode", "pos": [150, 390], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 301}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [97]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": ["过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走,"], "color": "#322", "bgcolor": "#533"}, {"id": 151, "type": "PathchSageAttentionKJ", "pos": [323.8629150390625, -547.4509887695312], "size": [315, 58], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 227}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [231]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 152, "type": "UnetLoaderGGUF", "pos": [-475.4167785644531, -862.1105346679688], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [228]}], "properties": {"Node name for S&R": "UnetLoaderGGUF", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.2_T2V_High_Noise_14B_VACE-Q8_0.gguf"]}, {"id": 153, "type": "PathchSageAttentionKJ", "pos": [-102.09554290771484, -1009.4180297851562], "size": [315, 58], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 228}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [229]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 154, "type": "LoraLoaderModelOnly", "pos": [337.5032958984375, -1012.3259887695312], "size": [315, 82], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 229}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [232]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1]}, {"id": 155, "type": "LoraLoaderModelOnly", "pos": [3.2598719596862793, -717.4114379882812], "size": [315, 82], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 230}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [227]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1]}, {"id": 157, "type": "ModelSamplingSD3", "pos": [690.647216796875, -465.15863037109375], "size": [315, 58], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 231}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [238]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002]}, {"id": 158, "type": "ModelSamplingSD3", "pos": [828.9415283203125, -983.5126342773438], "size": [315, 58], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 232}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [234]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002]}, {"id": 161, "type": "KSamplerAdvanced", "pos": [1316.547607421875, -831.2813110351562], "size": [304.748046875, 334], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 234}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 243}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 244}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 299}, {"label": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": 235}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [239]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["enable", 805600082939279, "randomize", 8, 1, "lcm", "simple", 0, 4, "enable"]}, {"id": 164, "type": "KSamplerAdvanced", "pos": [1705.3363037109375, -835.4957885742188], "size": [304.748046875, 334], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 238}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 302}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 303}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 239}, {"label": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": 240}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [236]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 805600082939279, "randomize", 8, 1, "lcm", "simple", 4, 10000, "disable"]}, {"id": 165, "type": "VAEDecode", "pos": [2293.0859375, -523.4891967773438], "size": [315, 46], "flags": {"collapsed": false}, "order": 47, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 241}, {"label": "vae", "name": "vae", "type": "VAE", "link": 246}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [237]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 162, "type": "TrimVideoLatent", "pos": [1900.22509765625, -276.7439880371094], "size": [315, 60], "flags": {"collapsed": false}, "order": 46, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 236}, {"label": "trim_amount", "name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 245}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [241]}], "properties": {"Node name for S&R": "TrimVideoLatent", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [0]}, {"id": 105, "type": "VAELoader", "pos": [-260, 530], "size": [350, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [183, 246]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.34", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}]}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 156, "type": "UnetLoaderGGUF", "pos": [-448.0536193847656, -688.684326171875], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [230]}], "properties": {"Node name for S&R": "UnetLoaderGGUF", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.2_T2V_Low_Noise_14B_VACE-Q8_0.gguf"]}, {"id": 167, "type": "WanVideoVAELoader", "pos": [4461.998046875, 1272.03173828125], "size": [315, 82], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "links": []}], "properties": {"Node name for S&R": "WanVideoVAELoader", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#223", "bgcolor": "#335"}, {"id": 174, "type": "ImageMirror", "pos": [3574.870361328125, 1160.4659423828125], "size": [315, 58], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 249}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [285]}], "properties": {"Node name for S&R": "ImageMirror", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["horizontal"], "color": "#223", "bgcolor": "#335"}, {"id": 178, "type": "DepthAnything_V2", "pos": [4987.8193359375, 392.3339538574219], "size": [214.20001220703125, 46], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "da_model", "name": "da_model", "type": "DAMODEL", "link": 256}, {"label": "images", "name": "images", "type": "IMAGE", "link": 257}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [261]}], "properties": {"Node name for S&R": "DepthAnything_V2", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 179, "type": "OpenposePreprocessor", "pos": [4786.18505859375, 801.0087280273438], "size": [315, 174], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 258}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [262]}, {"label": "POSE_KEYPOINT", "name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "properties": {"Node name for S&R": "OpenposePreprocessor", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["enable", "enable", "enable", 512, "disable"], "color": "#223", "bgcolor": "#335"}, {"id": 180, "type": "ImageFromBatch+", "pos": [4993.2158203125, 1208.903076171875], "size": [315, 82], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 259}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [281]}], "properties": {"Node name for S&R": "ImageFromBatch+", "cnr_id": "comfyui_essentials", "ver": "33ff89fd354d8ec3ab6affb605a79a931b445d99", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [3, -1], "color": "#223", "bgcolor": "#335"}, {"id": 192, "type": "JWInteger", "pos": [3180.7939453125, 1803.3526611328125], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [290]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [640], "color": "#223", "bgcolor": "#335"}, {"id": 195, "type": "LoadWanVideoT5TextEncoder", "pos": [3787.3818359375, 747.4342041015625], "size": [352.79998779296875, 130], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "links": []}], "properties": {"Node name for S&R": "LoadWanVideoT5TextEncoder", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#223", "bgcolor": "#335"}, {"id": 196, "type": "VHS_VideoCombine", "pos": [6854.0087890625, 1909.3565673828125], "size": [696.3893432617188, 358], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 272}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "972c87da577b47211c4e9aeed30dc38c7bae607f", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_I2V_00006_zjdhx_1752755397.mp4", "workflow": "WanVideoWrapper_I2V_00006.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/WanVideoWrapper_I2V_00006.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}}, "color": "#223", "bgcolor": "#335"}, {"id": 199, "type": "DownloadAndLoadDepthAnythingV2Model", "pos": [4333.087890625, 380.9408874511719], "size": [441, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "da_v2_model", "name": "da_v2_model", "type": "DAMODEL", "links": [256]}], "properties": {"Node name for S&R": "DownloadAndLoadDepthAnythingV2Model", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["depth_anything_v2_vitl_fp32.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 202, "type": "VHS_MergeImages", "pos": [5433.31982421875, 1077.248046875], "size": [285.2860412597656, 137.1524658203125], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "images_A", "name": "images_A", "type": "IMAGE", "link": 280}, {"label": "images_B", "name": "images_B", "type": "IMAGE", "link": 281}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [271]}, {"label": "count", "name": "count", "type": "INT"}], "properties": {"Node name for S&R": "VHS_MergeImages", "cnr_id": "comfyui-videohelpersuite", "ver": "4c7858ddd5126f7293dc3c9f6e0fc4c263cde079", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": {"merge_strategy": "match A", "scale_method": "nearest-exact", "crop": "disabled"}, "color": "#223", "bgcolor": "#335"}, {"id": 207, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [3889.73828125, 1376.85302734375], "size": [336, 330], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 289}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 290}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [257, 258, 264]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT", "links": [283]}, {"label": "height", "name": "height", "type": "INT", "links": [284]}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2", "widget_ue_connectable": {}}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 640, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 190, "type": "VHS_VideoCombine", "pos": [6085.1201171875, 479.5652160644531], "size": [220.5830078125, 358], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 270}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "972c87da577b47211c4e9aeed30dc38c7bae607f", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": false, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_I2V_00001_odhhl_1753770135.mp4", "workflow": "WanVideoWrapper_I2V_00001.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/temp/WanVideoWrapper_I2V_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "temp", "frame_rate": 16}}}, "color": "#223", "bgcolor": "#335"}, {"id": 182, "type": "ImpactSwitch", "pos": [5090.0029296875, 36.02327346801758], "size": [315, 122], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "input1", "name": "input1", "shape": 7, "type": "IMAGE", "link": 261}, {"label": "input2", "name": "input2", "type": "IMAGE", "link": 262}, {"label": "input3", "name": "input3", "type": "IMAGE"}, {"label": "select", "name": "select", "type": "INT", "widget": {"name": "select"}, "link": 263}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [259, 270, 291]}, {"label": "selected_label", "name": "selected_label", "type": "STRING"}, {"label": "selected_index", "name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2, false], "color": "#223", "bgcolor": "#335"}, {"id": 205, "type": "ImpactSwitch", "pos": [3496.63525390625, 1933.1624755859375], "size": [315, 122], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "input1", "name": "input1", "shape": 7, "type": "IMAGE", "link": 285}, {"label": "input2", "name": "input2", "type": "IMAGE", "link": 286}, {"label": "input3", "name": "input3", "type": "IMAGE"}, {"label": "select", "name": "select", "type": "INT", "widget": {"name": "select"}, "link": 287}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [248, 266, 282]}, {"label": "selected_label", "name": "selected_label", "type": "STRING"}, {"label": "selected_index", "name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2, false], "color": "#223", "bgcolor": "#335"}, {"id": 204, "type": "ImageResizeKJ", "pos": [479.0082702636719, 853.0796508789062], "size": [315, 286], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 282}, {"label": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 283}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 284}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [260, 292]}, {"label": "width", "name": "width", "type": "INT", "links": [293]}, {"label": "height", "name": "height", "type": "INT", "links": [294]}], "properties": {"Node name for S&R": "ImageResizeKJ", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [512, 384, "lanc<PERSON>s", false, 2, 0], "color": "#223", "bgcolor": "#335"}, {"id": 206, "type": "VHS_LoadVideo", "pos": [-1574.2303466796875, 336.610595703125], "size": [626.64697265625, 1426.4835205078125], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"label": "frame_load_cap", "name": "frame_load_cap", "type": "INT", "widget": {"name": "frame_load_cap"}, "link": 288}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [289]}, {"label": "frame_count", "name": "frame_count", "type": "INT", "slot_index": 1, "links": []}, {"label": "audio", "name": "audio", "type": "AUDIO"}, {"label": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO", "slot_index": 3, "links": []}], "properties": {"Node name for S&R": "VHS_LoadVideo", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": {"video": "5af10b3f62e2df59aaeb47a04015a015a9ebfa8bbca6b5f8776a5a8db6d0d03d.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 0, "custom_height": 0, "frame_load_cap": 81, "skip_first_frames": 90, "select_every_nth": 1, "format": "AnimateDiff", "choose video to upload": "image", "videopreview": {"paused": false, "hidden": false, "params": {"custom_height": 0, "filename": "5af10b3f62e2df59aaeb47a04015a015a9ebfa8bbca6b5f8776a5a8db6d0d03d.mp4", "force_rate": 0, "custom_width": 0, "select_every_nth": 1, "frame_load_cap": 81, "format": "video/mp4", "skip_first_frames": 90, "type": "input"}}}, "color": "#223", "bgcolor": "#335"}, {"id": 186, "type": "Note", "pos": [-2032.2706298828125, 1887.4036865234375], "size": [210, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["参考图"], "color": "#432", "bgcolor": "#653"}, {"id": 194, "type": "Note", "pos": [-1486.105224609375, 3405.103271484375], "size": [210, 88], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["提示词"], "color": "#432", "bgcolor": "#653"}, {"id": 184, "type": "DF_Integer", "pos": [-1213.9735107421875, 3159.292724609375], "size": [315, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [288, 296]}], "properties": {"Node name for S&R": "DF_Integer", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [81], "color": "#223", "bgcolor": "#335"}, {"id": 172, "type": "Note", "pos": [-1461.8199462890625, 3138.345947265625], "size": [210, 88], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["总帧数"], "color": "#432", "bgcolor": "#653"}, {"id": 159, "type": "PrimitiveNode", "pos": [741.954833984375, -294.7231140136719], "size": [210, 82], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "widget": {"name": "noise_seed"}, "links": [235, 240]}], "title": "noise_seed", "properties": {"Run widget replace on values": false}, "widgets_values": [805600082939279, "randomize"]}, {"id": 183, "type": "JWInteger", "pos": [-1345.322265625, -116.75938415527344], "size": [315, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [263]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2], "color": "#223", "bgcolor": "#335"}, {"id": 185, "type": "Note", "pos": [-1581.738525390625, -135.5712127685547], "size": [210, 88], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["选项1 深度方式，容易有改变参考图\n选项2 骨骼方式，更好的保留参考图"], "color": "#432", "bgcolor": "#653"}, {"id": 173, "type": "Note", "pos": [-1906.392578125, 462.63543701171875], "size": [210, 88], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["参考视频"], "color": "#432", "bgcolor": "#653"}, {"id": 168, "type": "Note", "pos": [-1654.3319091796875, -381.0079345703125], "size": [210, 88], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["值1是人物左右镜像图\n值2是原图"], "color": "#432", "bgcolor": "#653"}, {"id": 169, "type": "JWInteger", "pos": [-1401.222900390625, -356.9513854980469], "size": [315, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [287]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2], "color": "#223", "bgcolor": "#335"}, {"id": 171, "type": "PreviewImage", "pos": [4132.841796875, 1911.9639892578125], "size": [396.5025329589844, 396.7655029296875], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 248}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 181, "type": "ImageRepeat", "pos": [4868.28759765625, 1908.9345703125], "size": [315, 58], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 260}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [247, 280]}], "properties": {"Node name for S&R": "ImageRepeat", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [3], "color": "#223", "bgcolor": "#335"}, {"id": 170, "type": "PreviewImage", "pos": [4941.93115234375, 2137.906005859375], "size": [210, 246], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 247}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 187, "type": "ImageConcatMulti", "pos": [6354.6005859375, 1914.3634033203125], "size": [315, 170], "flags": {}, "order": 50, "mode": 0, "inputs": [{"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 264}, {"label": "image_2", "name": "image_2", "type": "IMAGE", "link": 297}, {"label": "image_3", "name": "image_3", "type": "IMAGE", "link": 266}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [272]}], "properties": {"widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [3, "right", true, null], "color": "#223", "bgcolor": "#335"}, {"id": 191, "type": "VHS_VideoCombine", "pos": [5400.0703125, 1904.174560546875], "size": [696.3893432617188, 358], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 271}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "972c87da577b47211c4e9aeed30dc38c7bae607f", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_I2V_00001_ukkvo_1753770142.mp4", "workflow": "WanVideoWrapper_I2V_00001.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/WanVideoWrapper_I2V_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}}, "color": "#223", "bgcolor": "#335"}, {"id": 49, "type": "WanVaceToVideo", "pos": [668.2416381835938, 346.125244140625], "size": [315, 254], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 96}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 97}, {"label": "vae", "name": "vae", "type": "VAE", "link": 183}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 291}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK"}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 292}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 293}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 294}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 296}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [243, 302]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [244, 303]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": [299]}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": [245]}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": [720, 720, 81, 1, 1]}, {"id": 163, "type": "ImageFromBatch+", "pos": [3006.1416015625, -529.5934448242188], "size": [315, 82], "flags": {}, "order": 48, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 237}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [233, 297]}], "properties": {"Node name for S&R": "ImageFromBatch+", "cnr_id": "comfyui_essentials", "ver": "33ff89fd354d8ec3ab6affb605a79a931b445d99", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [1108, -1], "color": "#223", "bgcolor": "#335"}, {"id": 160, "type": "VHS_VideoCombine", "pos": [3590.158935546875, -855.2666625976562], "size": [537.25732421875, 358], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 233}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "T8", "format": "video/h264-mp4.json", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "T8_00006_soxgz_1753767878.mp4", "workflow": "T8_00006.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/T8_00006.mp4", "format": "video/h264-mp4.json", "subfolder": "", "type": "output", "frame_rate": 16}}}}, {"id": 203, "type": "LoadImage", "pos": [-1493.555908203125, 1781.365966796875], "size": [709.1163330078125, 1166.5875244140625], "flags": {}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [249, 286]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.27", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["21606c3e26667d5692dd40ee23c5663a3ac73bed8084e51ac3821c45e0a75934.png", "image", ""], "color": "#223", "bgcolor": "#335"}, {"id": 6, "type": "CLIPTextEncode", "pos": [-270.7843933105469, 3081.374267578125], "size": [420, 290], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 300}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 305}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [96]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": ["一个穿蓝色衣服女人在屋里跳舞"], "color": "#232", "bgcolor": "#353"}, {"id": 193, "type": "CR Prompt Text", "pos": [-1266.88427734375, 3380.033203125], "size": [400, 200], "flags": {}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"label": "prompt", "name": "prompt", "type": "STRING", "links": [305]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Prompt Text", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["一个穿蓝色衣服女人在屋里跳舞"], "color": "#223", "bgcolor": "#335"}], "links": [[96, 6, 0, 49, 0, "CONDITIONING"], [97, 7, 0, 49, 1, "CONDITIONING"], [183, 105, 0, 49, 2, "VAE"], [227, 155, 0, 151, 0, "MODEL"], [228, 152, 0, 153, 0, "MODEL"], [229, 153, 0, 154, 0, "MODEL"], [230, 156, 0, 155, 0, "MODEL"], [231, 151, 0, 157, 0, "MODEL"], [232, 154, 0, 158, 0, "MODEL"], [233, 163, 0, 160, 0, "IMAGE"], [234, 158, 0, 161, 0, "MODEL"], [235, 159, 0, 161, 4, "INT"], [236, 164, 0, 162, 0, "LATENT"], [237, 165, 0, 163, 0, "IMAGE"], [238, 157, 0, 164, 0, "MODEL"], [239, 161, 0, 164, 3, "LATENT"], [240, 159, 0, 164, 4, "INT"], [241, 162, 0, 165, 0, "LATENT"], [243, 49, 0, 161, 1, "CONDITIONING"], [244, 49, 1, 161, 2, "CONDITIONING"], [245, 49, 3, 162, 1, "INT"], [246, 105, 0, 165, 1, "VAE"], [247, 181, 0, 170, 0, "IMAGE"], [248, 205, 0, 171, 0, "IMAGE"], [249, 203, 0, 174, 0, "IMAGE"], [256, 199, 0, 178, 0, "DAMODEL"], [257, 207, 0, 178, 1, "IMAGE"], [258, 207, 0, 179, 0, "IMAGE"], [259, 182, 0, 180, 0, "IMAGE"], [260, 204, 0, 181, 0, "IMAGE"], [261, 178, 0, 182, 0, "IMAGE"], [262, 179, 0, 182, 1, "IMAGE"], [263, 183, 0, 182, 3, "INT"], [264, 207, 0, 187, 0, "IMAGE"], [266, 205, 0, 187, 2, "IMAGE"], [270, 182, 0, 190, 0, "IMAGE"], [271, 202, 0, 191, 0, "IMAGE"], [272, 187, 0, 196, 0, "IMAGE"], [280, 181, 0, 202, 0, "IMAGE"], [281, 180, 0, 202, 1, "IMAGE"], [282, 205, 0, 204, 0, "IMAGE"], [283, 207, 3, 204, 2, "INT"], [284, 207, 4, 204, 3, "INT"], [285, 174, 0, 205, 0, "IMAGE"], [286, 203, 0, 205, 1, "IMAGE"], [287, 169, 0, 205, 3, "INT"], [288, 184, 0, 206, 2, "INT"], [289, 206, 0, 207, 0, "IMAGE"], [290, 192, 0, 207, 2, "INT"], [291, 182, 0, 49, 3, "IMAGE"], [292, 204, 0, 49, 5, "IMAGE"], [293, 204, 1, 49, 6, "INT"], [294, 204, 2, 49, 7, "INT"], [296, 184, 0, 49, 8, "INT"], [297, 163, 0, 187, 1, "IMAGE"], [299, 49, 2, 161, 3, "LATENT"], [300, 110, 0, 6, 0, "CLIP"], [301, 110, 0, 7, 0, "CLIP"], [302, 49, 0, 164, 1, "CONDITIONING"], [303, 49, 1, 164, 2, "CONDITIONING"], [305, 193, 0, 6, 1, "STRING"]], "groups": [{"id": 16, "title": "B站、Youtube：T8star-Aix", "bounding": [-1571.1962890625, -1632.721435546875, 4168, 309], "color": "#3f789e", "font_size": 240, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7972024500000005, "offset": [2001.9873731759308, -2867.3578361589307]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "node_versions": {"comfy-core": "0.3.34"}}, "version": 0.4}