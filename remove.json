{"id": "7cbcec68-7fa6-47bb-a38a-da689949a001", "revision": 0, "last_node_id": 196, "last_link_id": 302, "nodes": [{"id": 39, "type": "VAELoader", "pos": [-397.5133972167969, 383.81854248046875], "size": [337.76861572265625, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "vae名称", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"label": "VAE", "localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [61, 223]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 135, "type": "ConditioningZeroOut", "pos": [250, 200], "size": [240, 26], "flags": {"collapsed": false}, "order": 9, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 237}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [238]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "ConditioningZeroOut", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 173, "type": "PreviewImage", "pos": [320, 860], "size": [420, 310], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "images", "localized_name": "图像", "name": "images", "type": "IMAGE", "link": 289}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 8, "type": "VAEDecode", "pos": [530, 350], "size": [190, 46], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"label": "samples", "localized_name": "Latent", "name": "samples", "type": "LATENT", "link": 52}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 61}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [240]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 124, "type": "VAEEncode", "pos": [-20, 400], "size": [240, 50], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"label": "pixels", "localized_name": "像素", "name": "pixels", "type": "IMAGE", "link": 222}, {"label": "vae", "localized_name": "vae", "name": "vae", "type": "VAE", "link": 223}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "links": [291, 293]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEEncode", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 42, "type": "FluxKontextImageScale", "pos": [-50, 570], "size": [270, 30], "flags": {"collapsed": false}, "order": 11, "mode": 0, "inputs": [{"label": "image", "localized_name": "image", "name": "image", "type": "IMAGE", "link": 251}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [222, 289]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 175, "type": "<PERSON>downNote", "pos": [-50, 640], "size": [320, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "title": "About Flux Kontext Edit", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["[English] Use Ctrl + B to enable multipule image input.\n\n[中文] 使用 **Ctrl + B** 来启用多张图片输入"], "color": "#432", "bgcolor": "#653"}, {"id": 35, "type": "FluxGuidance", "pos": [250, 90], "size": [240, 58], "flags": {"collapsed": false}, "order": 15, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "条件", "name": "conditioning", "type": "CONDITIONING", "link": 292}, {"localized_name": "引导", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxGuidance", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [2.5], "color": "#223", "bgcolor": "#335"}, {"id": 177, "type": "ReferenceLatent", "pos": [10, 140], "size": [211.60000610351562, 46], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "conditioning", "localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 294}, {"label": "latent", "localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 293}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "links": [292]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ReferenceLatent", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 38, "type": "DualCLIPLoader", "pos": [-397.5133972167969, 203.8184356689453], "size": [337.76861572265625, 130], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "CLIP名称1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "CLIP名称2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "类型", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "设备", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"label": "CLIP", "localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [59]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "DualCLIPLoader", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 146, "type": "ImageStitch", "pos": [-390, 570], "size": [270, 150], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "image1", "localized_name": "image1", "name": "image1", "type": "IMAGE", "link": 296}, {"label": "image2", "localized_name": "image2", "name": "image2", "shape": 7, "type": "IMAGE", "link": 250}, {"localized_name": "direction", "name": "direction", "type": "COMBO", "widget": {"name": "direction"}, "link": null}, {"localized_name": "match_image_size", "name": "match_image_size", "type": "BOOLEAN", "widget": {"name": "match_image_size"}, "link": null}, {"localized_name": "spacing_width", "name": "spacing_width", "type": "INT", "widget": {"name": "spacing_width"}, "link": null}, {"localized_name": "spacing_color", "name": "spacing_color", "type": "COMBO", "widget": {"name": "spacing_color"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [251]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "ImageStitch", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["right", true, 0, "white"], "color": "#223", "bgcolor": "#335"}, {"id": 136, "type": "SaveImage", "pos": [993.2583618164062, 511.5040588378906], "size": [650, 660], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "images", "localized_name": "图片", "name": "images", "type": "IMAGE", "link": 240}, {"localized_name": "文件名前缀", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["ComfyUI"], "color": "#223", "bgcolor": "#335"}, {"id": 31, "type": "K<PERSON><PERSON><PERSON>", "pos": [642.3905639648438, -21.188167572021484], "size": [320, 474], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 302}, {"label": "positive", "localized_name": "正面条件", "name": "positive", "type": "CONDITIONING", "link": 57}, {"label": "negative", "localized_name": "负面条件", "name": "negative", "type": "CONDITIONING", "link": 238}, {"label": "latent_image", "localized_name": "Latent图像", "name": "latent_image", "type": "LATENT", "link": 291}, {"localized_name": "种子", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "步数", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "采样器名称", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "调度器", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "降噪", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"label": "LATENT", "localized_name": "Latent", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [709813378207560, "randomize", 20, 1, "euler", "simple", 1], "color": "#223", "bgcolor": "#335"}, {"id": 147, "type": "LoadImageOutput", "pos": [-50, 770], "size": [320, 374], "flags": {}, "order": 3, "mode": 4, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [250]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImageOutput", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_00252_.png [output]", false, "refresh", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "CLIPTextEncode", "pos": [330, 560], "size": [400, 220], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "clip", "localized_name": "clip", "name": "clip", "type": "CLIP", "link": 59}, {"label": "text", "localized_name": "文本", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"label": "CONDITIONING", "localized_name": "条件", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [237, 294]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["Remove the girl's clothes", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 195, "type": "LoraLoaderModelOnly", "pos": [-431.58050537109375, -74.53668212890625], "size": [270, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "model", "localized_name": "模型", "name": "model", "type": "MODEL", "link": 300}, {"localized_name": "LoRA名称", "name": "lora_name", "type": "COMBO", "widget": {"name": "lora_name"}, "link": null}, {"localized_name": "模型强度", "name": "strength_model", "type": "FLOAT", "widget": {"name": "strength_model"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [301]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["clothes_remover_v0.safetensors", 1], "color": "#223", "bgcolor": "#335"}, {"id": 194, "type": "UnetLoaderGGUF", "pos": [-724.223876953125, -63.31243896484375], "size": [270, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [300]}], "properties": {"cnr_id": "comfyui-gguf", "ver": "b3ec875a68d94b758914fd48d30571d953bb7a54", "Node name for S&R": "UnetLoaderGGUF", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["flux1-kontext-dev-Q8_0.gguf"], "color": "#223", "bgcolor": "#335"}, {"id": 196, "type": "MZ_Flux1PartialLoad_Patch", "pos": [-87.38993072509766, -124.46453857421875], "size": [330.2437438964844, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "model", "localized_name": "model", "name": "model", "type": "MODEL", "link": 301}, {"localized_name": "double_blocks_cuda_size", "name": "double_blocks_cuda_size", "type": "INT", "widget": {"name": "double_blocks_cuda_size"}, "link": null}, {"localized_name": "single_blocks_cuda_size", "name": "single_blocks_cuda_size", "type": "INT", "widget": {"name": "single_blocks_cuda_size"}, "link": null}], "outputs": [{"label": "MODEL", "localized_name": "模型", "name": "MODEL", "type": "MODEL", "links": [302]}], "properties": {"cnr_id": "ComfyUI-FluxExt-MZ", "ver": "00e4b70dea2b1d9b3f99a4fd267687bc69bdda98", "Node name for S&R": "MZ_Flux1PartialLoad_Patch", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [7, 7], "color": "#223", "bgcolor": "#335"}, {"id": 191, "type": "LoadImage", "pos": [-373.5699768066406, 801.457275390625], "size": [270, 314], "flags": {}, "order": 5, "mode": 0, "inputs": [{"localized_name": "图像", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "选择文件上传", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"label": "IMAGE", "localized_name": "图像", "name": "IMAGE", "type": "IMAGE", "links": [296]}, {"label": "MASK", "localized_name": "遮罩", "name": "MASK", "type": "MASK"}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "Node name for S&R": "LoadImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["Gsut_Rxb0AAPJXO.jfif", "image"], "color": "#223", "bgcolor": "#335"}], "links": [[52, 31, 0, 8, 0, "LATENT"], [57, 35, 0, 31, 1, "CONDITIONING"], [59, 38, 0, 6, 0, "CLIP"], [61, 39, 0, 8, 1, "VAE"], [222, 42, 0, 124, 0, "IMAGE"], [223, 39, 0, 124, 1, "VAE"], [237, 6, 0, 135, 0, "CONDITIONING"], [238, 135, 0, 31, 2, "CONDITIONING"], [240, 8, 0, 136, 0, "IMAGE"], [250, 147, 0, 146, 1, "IMAGE"], [251, 146, 0, 42, 0, "IMAGE"], [289, 42, 0, 173, 0, "IMAGE"], [291, 124, 0, 31, 3, "LATENT"], [292, 177, 0, 35, 0, "CONDITIONING"], [293, 124, 0, 177, 1, "LATENT"], [294, 6, 0, 177, 0, "CONDITIONING"], [296, 191, 0, 146, 0, "IMAGE"], [300, 194, 0, 195, 0, "MODEL"], [301, 195, 0, 196, 0, "MODEL"], [302, 196, 0, 31, 0, "MODEL"]], "groups": [{"id": 3, "title": "Step 2 - Upload images", "bounding": [-410, 480, 700, 680], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "Step 3 - Prompt", "bounding": [310, 480, 430, 330], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "Conditioning", "bounding": [-30, 10, 540, 250], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "B站、Youtube：T8star-Aix", "bounding": [-1549.734130859375, -712.2920532226562, 4168, 309], "color": "#3f789e", "font_size": 240, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.6588450000000052, "offset": [823.437571394475, 310.204698435496]}, "frontendVersion": "1.23.4", "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false, "groupNodes": {}}, "version": 0.4}