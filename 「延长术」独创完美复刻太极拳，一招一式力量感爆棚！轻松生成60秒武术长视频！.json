{"id": "345e0e8a-c0f7-48e7-8536-443517b7d519", "revision": 0, "last_node_id": 1092, "last_link_id": 1700, "nodes": [{"id": 163, "type": "GetNode", "pos": [-228.0386962890625, -854.5184936523438], "size": [210, 60], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [209]}], "title": "Get_wan_model", "properties": {}, "widgets_values": ["wan_model"], "color": "#223", "bgcolor": "#335"}, {"id": 172, "type": "GetNode", "pos": [-229.9681396484375, -548.6751098632812], "size": [210, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [218, 960]}], "title": "Get_width", "properties": {}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 173, "type": "GetNode", "pos": [-231.8800048828125, -500.37847900390625], "size": [210, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [219, 961]}], "title": "Get_height", "properties": {}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 171, "type": "GetNode", "pos": [-227.2510986328125, -453.7067565917969], "size": [210, 58], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [220, 956, 962, 1310]}], "title": "Get_length", "properties": {}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 446, "type": "SetNode", "pos": [-5344.23681640625, 72.73271942138672], "size": [210, 58], "flags": {"collapsed": true}, "order": 94, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 646}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_steps", "properties": {"previousName": ""}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 447, "type": "SetNode", "pos": [-5347.02685546875, 196.05577087402344], "size": [210, 58], "flags": {"collapsed": true}, "order": 85, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 647}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_seed", "properties": {"previousName": ""}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 140, "type": "SetNode", "pos": [-5461.58447265625, -191.99349975585938], "size": [210, 58], "flags": {"collapsed": true}, "order": 91, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 171}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_length", "properties": {"previousName": "length"}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 139, "type": "SetNode", "pos": [-5465.57763671875, -305.1285705566406], "size": [210, 58], "flags": {"collapsed": true}, "order": 87, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 170}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_height", "properties": {"previousName": "height"}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 448, "type": "INTConstant", "pos": [-5716.68603515625, 168.135986328125], "size": [298.4490966796875, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [647]}], "title": "Seed", "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d"}, "widgets_values": [40], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 138, "type": "SetNode", "pos": [-5472.23291015625, -408.94598388671875], "size": [210, 60], "flags": {"collapsed": true}, "order": 88, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 169}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_width", "properties": {"previousName": "width"}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 152, "type": "SetNode", "pos": [-5387.69580078125, -49.9175910949707], "size": [210, 58], "flags": {"collapsed": true}, "order": 95, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "link": 194}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_int_overlap_frames", "properties": {"previousName": "int_overlap_frames"}, "widgets_values": ["int_overlap_frames"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 459, "type": "Note", "pos": [-2288.7490234375, -976.5023193359375], "size": [211.1167755126953, 88], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["optional Lora"], "color": "#432", "bgcolor": "#653"}, {"id": 27, "type": "ModelSamplingSD3", "pos": [-1877.3485107421875, -967.3145141601562], "size": [221.0778350830078, 58], "flags": {}, "order": 134, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 768}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [671]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": [8.000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 129, "type": "SetNode", "pos": [-1618.939208984375, -939.4541015625], "size": [210, 60], "flags": {"collapsed": true}, "order": 143, "mode": 0, "inputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "link": 671}], "outputs": [{"label": "*", "name": "*", "type": "*", "links": []}], "title": "Set_wan_model", "properties": {"previousName": "wan_model"}, "widgets_values": ["wan_model"], "color": "#223", "bgcolor": "#335"}, {"id": 131, "type": "SetNode", "pos": [-1741.23095703125, -791.9968872070312], "size": [210, 60], "flags": {"collapsed": true}, "order": 135, "mode": 0, "inputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "link": 769}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_wan_clip", "properties": {"previousName": "wan_clip"}, "widgets_values": ["wan_clip"], "color": "#432", "bgcolor": "#653"}, {"id": 168, "type": "GetNode", "pos": [-232.3956298828125, -794.3876953125], "size": [210, 60], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [214]}], "title": "Get_pos_prompt", "properties": {}, "widgets_values": ["pos_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 169, "type": "GetNode", "pos": [-232.2049560546875, -726.5296020507812], "size": [210, 58], "flags": {"collapsed": true}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [215]}], "title": "Get_neg_prompt", "properties": {}, "widgets_values": ["neg_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 130, "type": "SetNode", "pos": [-2565.4951171875, -365.3686218261719], "size": [210, 60], "flags": {"collapsed": true}, "order": 86, "mode": 0, "inputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "link": 155}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_wan_vae", "properties": {"previousName": "wan_vae"}, "widgets_values": ["wan_vae"], "color": "#322", "bgcolor": "#533"}, {"id": 363, "type": "SetNode", "pos": [-3549.7529296875, -663.2498779296875], "size": [210, 60], "flags": {"collapsed": true}, "order": 137, "mode": 0, "inputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "link": 712}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_video_mask", "properties": {"previousName": "video_mask"}, "widgets_values": ["video_mask"], "color": "#1c5715", "bgcolor": "#1f401b"}, {"id": 90, "type": "SetNode", "pos": [-3549.95751953125, -553.3197021484375], "size": [210, 58], "flags": {"collapsed": true}, "order": 105, "mode": 0, "inputs": [{"label": "AUDIO", "name": "AUDIO", "type": "AUDIO", "link": 685}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_video_src-audio", "properties": {"previousName": ""}, "widgets_values": ["video_src-audio"]}, {"id": 475, "type": "GroundingDinoModelLoader (segment anything)", "pos": [-4441.447265625, -528.232177734375], "size": [361.20001220703125, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "GROUNDING_DINO_MODEL", "name": "GROUNDING_DINO_MODEL", "type": "GROUNDING_DINO_MODEL", "links": [701]}], "properties": {"Node name for S&R": "GroundingDinoModelLoader (segment anything)", "cnr_id": "comfyui_segment_anything", "ver": "ab6395596399d5048639cdab7e44ec9fae857a93"}, "widgets_values": ["GroundingDINO_SwinB (938MB)"]}, {"id": 95, "type": "SetNode", "pos": [-3545.02392578125, -333.5506896972656], "size": [210, 58], "flags": {"collapsed": true}, "order": 120, "mode": 0, "inputs": [{"label": "FLOAT", "name": "FLOAT", "type": "FLOAT", "link": 105}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_video_fps", "properties": {"previousName": ""}, "widgets_values": ["video_fps"], "color": "#232", "bgcolor": "#353"}, {"id": 91, "type": "VHS_VideoInfo", "pos": [-3875.4306640625, -364.8321533203125], "size": [262, 206], "flags": {}, "order": 106, "mode": 0, "inputs": [{"label": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO", "link": 686}], "outputs": [{"label": "source_fps🟨", "name": "source_fps🟨", "type": "FLOAT", "links": [105]}, {"label": "source_frame_count🟨", "name": "source_frame_count🟨", "type": "INT"}, {"label": "source_duration🟨", "name": "source_duration🟨", "type": "FLOAT"}, {"label": "source_width🟨", "name": "source_width🟨", "type": "INT"}, {"label": "source_height🟨", "name": "source_height🟨", "type": "INT"}, {"label": "loaded_fps🟦", "name": "loaded_fps🟦", "type": "FLOAT"}, {"label": "loaded_frame_count🟦", "name": "loaded_frame_count🟦", "type": "INT"}, {"label": "loaded_duration🟦", "name": "loaded_duration🟦", "type": "FLOAT"}, {"label": "loaded_width🟦", "name": "loaded_width🟦", "type": "INT"}, {"label": "loaded_height🟦", "name": "loaded_height🟦", "type": "INT"}], "properties": {"Node name for S&R": "VHS_VideoInfo", "cnr_id": "comfyui-videohelpersuite", "ver": "df55f01d1df2f7bf5cc772294bc2e6d8bab22d66"}, "widgets_values": {}}, {"id": 478, "type": "SAMModelLoader (segment anything)", "pos": [-4444.9072265625, -639.8089599609375], "size": [335.6919860839844, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "SAM_MODEL", "name": "SAM_MODEL", "type": "SAM_MODEL", "links": [702]}], "properties": {"Node name for S&R": "SAMModelLoader (segment anything)", "cnr_id": "comfyui_segment_anything", "ver": "ab6395596399d5048639cdab7e44ec9fae857a93"}, "widgets_values": ["sam_vit_h (2.56GB)"]}, {"id": 13, "type": "CLIPLoader", "pos": [-3179.244140625, -708.2902221679688], "size": [361.29998779296875, 106], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [668, 737, 767]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": ["umt5_xxl_fp16.safetensors", "wan", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 14, "type": "VAELoader", "pos": [-3175.860595703125, -513.7013549804688], "size": [371.20001220703125, 58], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [155]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": ["Wan2.1_VAE.pth"], "color": "#322", "bgcolor": "#533"}, {"id": 89, "type": "SetNode", "pos": [-3473.626953125, -936.2453002929688], "size": [210, 58], "flags": {"collapsed": true}, "order": 149, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 711}], "outputs": [{"label": "*", "name": "*", "type": "*", "links": []}], "title": "Set_video_src", "properties": {"previousName": ""}, "widgets_values": ["video_src"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 479, "type": "Mix Color By Mask", "pos": [-3825.53759765625, -953.7332153320312], "size": [315, 126], "flags": {}, "order": 144, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 709}, {"label": "mask", "name": "mask", "type": "IMAGE", "link": 707}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [711]}], "properties": {"Node name for S&R": "Mix Color By Mask", "cnr_id": "masquerade", "ver": "432cb4d146a391b387a0cd25ace824328b5b61cf"}, "widgets_values": [255, 255, 255]}, {"id": 180, "type": "GetNode", "pos": [-241.9781494140625, -661.5953369140625], "size": [210, 60], "flags": {"collapsed": true}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [495, 781, 1313]}], "title": "Get_video_src", "properties": {}, "widgets_values": ["video_src"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 20, "type": "INTConstant", "pos": [-5738.51123046875, -330.6319580078125], "size": [210, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [170]}], "title": "height", "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d"}, "widgets_values": [1024], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 19, "type": "INTConstant", "pos": [-5738.96630859375, -444.30987548828125], "size": [210, 58], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [169]}], "title": "widh", "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d"}, "widgets_values": [576], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 85, "type": "GetNode", "pos": [-4844.9326171875, -949.0451049804688], "size": [210, 58], "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [185]}], "title": "Get_width", "properties": {}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 87, "type": "GetNode", "pos": [-4842.45703125, -890.8024291992188], "size": [210, 58], "flags": {"collapsed": true}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [186]}], "title": "Get_height", "properties": {}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 182, "type": "GetNode", "pos": [-235.96861267089844, -391.6118469238281], "size": [210, 58], "flags": {"collapsed": true}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "links": [503, 783, 1314]}], "title": "Get_video_mask", "properties": {}, "widgets_values": ["video_mask"], "color": "#1c5715", "bgcolor": "#1f401b"}, {"id": 164, "type": "GetNode", "pos": [-3164.267578125, 84.44429016113281], "size": [210, 58], "flags": {"collapsed": true}, "order": 18, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [210, 211]}], "title": "Get_wan_clip", "properties": {}, "widgets_values": ["wan_clip"], "color": "#432", "bgcolor": "#653"}, {"id": 166, "type": "SetNode", "pos": [-1752.5828857421875, -56.4181022644043], "size": [210, 60], "flags": {}, "order": 107, "mode": 0, "inputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "link": 212}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_pos_prompt", "properties": {"previousName": "pos_prompt"}, "widgets_values": ["pos_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 167, "type": "SetNode", "pos": [-1744.7493896484375, 181.51063537597656], "size": [210, 58], "flags": {}, "order": 108, "mode": 0, "inputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "link": 213}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_neg_prompt", "properties": {"previousName": "neg_prompt"}, "widgets_values": ["neg_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 518, "type": "GetNode", "pos": [-339.268798828125, 722.1459350585938], "size": [210, 58], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [835]}], "title": "Get_wan_model", "properties": {}, "widgets_values": ["wan_model"], "color": "#223", "bgcolor": "#335"}, {"id": 522, "type": "GetNode", "pos": [-343.626220703125, 782.277587890625], "size": [210, 58], "flags": {"collapsed": true}, "order": 20, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [826]}], "title": "Get_pos_prompt", "properties": {}, "widgets_values": ["pos_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 523, "type": "GetNode", "pos": [-343.435302734375, 850.1357421875], "size": [210, 58], "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [827]}], "title": "Get_neg_prompt", "properties": {}, "widgets_values": ["neg_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 529, "type": "GetNode", "pos": [-353.2082824707031, 915.0701293945312], "size": [210, 58], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [855]}], "title": "Get_video_src", "properties": {}, "widgets_values": ["video_src"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 21, "type": "INTConstant", "pos": [-5740.12255859375, -222.32626342773438], "size": [210, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [171]}], "title": "frames", "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d"}, "widgets_values": [81], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 541, "type": "GetNode", "pos": [759.8777465820312, 1429.25927734375], "size": [210, 60], "flags": {"collapsed": true}, "order": 24, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [844, 846, 880, 1666, 1698]}], "title": "Get_int_overlap_frames", "properties": {}, "widgets_values": ["int_overlap_frames"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 525, "type": "VAEDecode", "pos": [1461.3209228515625, 885.0731811523438], "size": [213.95693969726562, 54.918617248535156], "flags": {"collapsed": false}, "order": 164, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 821}, {"label": "vae", "name": "vae", "type": "VAE", "link": 822}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [876]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 589, "type": "GetNode", "pos": [-4633.65185546875, 363.2214660644531], "size": [210, 58], "flags": {"collapsed": true}, "order": 25, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [939]}], "title": "Get_height", "properties": {}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 588, "type": "GetNode", "pos": [-4627.56298828125, 316.5198974609375], "size": [210, 60], "flags": {"collapsed": true}, "order": 26, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [938]}], "title": "Get_width", "properties": {}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 480, "type": "MaskToImage", "pos": [-3988.07666015625, -770.4295043945312], "size": [208.7832794189453, 26], "flags": {}, "order": 136, "mode": 0, "inputs": [{"label": "mask", "name": "mask", "type": "MASK", "link": 708}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [707]}], "properties": {"Node name for S&R": "MaskToImage", "cnr_id": "comfy-core", "ver": "0.3.29"}, "widgets_values": []}, {"id": 31, "type": "VAEDecode", "pos": [1545.5267333984375, -671.77294921875], "size": [213.95693969726562, 54.918617248535156], "flags": {"collapsed": false}, "order": 146, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 32}, {"label": "vae", "name": "vae", "type": "VAE", "link": 217}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [224]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": []}, {"id": 471, "type": "SetNode", "pos": [-3553.822265625, 398.9312744140625], "size": [210, 58], "flags": {"collapsed": false}, "order": 124, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 1283}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_img_reference", "properties": {"previousName": "img_reference"}, "widgets_values": ["img_reference"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 454, "type": "GetNode", "pos": [1250.144775390625, -429.6485595703125], "size": [210, 58], "flags": {"collapsed": false}, "order": 27, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [654]}], "title": "Get_seed", "properties": {}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 24, "type": "K<PERSON><PERSON><PERSON>", "pos": [987.2646484375, -839.7506713867188], "size": [270, 262], "flags": {}, "order": 130, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 209}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 1013}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 1014}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 1015}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 654}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 645}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [36]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": [50, "fixed", 10, 1, "uni_pc", "simple", 1]}, {"id": 445, "type": "GetNode", "pos": [853.6182861328125, -712.7916870117188], "size": [210, 58], "flags": {"collapsed": true}, "order": 28, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [645]}], "title": "Get_steps", "properties": {}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 176, "type": "SetNode", "pos": [1527.4705810546875, -499.419921875], "size": [210, 60], "flags": {"collapsed": true}, "order": 151, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 224}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [660]}], "title": "Set_video_part1", "properties": {"previousName": "video_part1"}, "widgets_values": ["video_part1"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 609, "type": "GetNode", "pos": [-4008.807861328125, 1693.07421875], "size": [210, 58], "flags": {"collapsed": true}, "order": 29, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [999]}], "title": "Get_width", "properties": {}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 610, "type": "GetNode", "pos": [-4010.719970703125, 1741.37109375], "size": [210, 58], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1000]}], "title": "Get_height", "properties": {}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 612, "type": "GetNode", "pos": [-4011.235595703125, 1447.36181640625], "size": [210, 58], "flags": {"collapsed": true}, "order": 31, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [993]}], "title": "Get_pos_prompt", "properties": {}, "widgets_values": ["pos_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 613, "type": "GetNode", "pos": [-4011.045166015625, 1515.2197265625], "size": [210, 58], "flags": {"collapsed": true}, "order": 32, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [994]}], "title": "Get_neg_prompt", "properties": {}, "widgets_values": ["neg_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 631, "type": "GetNode", "pos": [-4004.309814453125, 1633.8720703125], "size": [210, 58], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [998]}], "title": "Get_img_reference", "properties": {}, "widgets_values": ["img_reference"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 641, "type": "GetNode", "pos": [-4167.19580078125, 2279.380859375], "size": [210, 58], "flags": {"collapsed": true}, "order": 34, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [1017]}], "title": "Get_wan_model", "properties": {}, "widgets_values": ["wan_model"], "color": "#223", "bgcolor": "#335"}, {"id": 642, "type": "GetNode", "pos": [-4169.12548828125, 2585.2236328125], "size": [210, 58], "flags": {"collapsed": true}, "order": 35, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1034]}], "title": "Get_width", "properties": {}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 643, "type": "GetNode", "pos": [-4171.03662109375, 2633.5205078125], "size": [210, 58], "flags": {"collapsed": true}, "order": 36, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1035]}], "title": "Get_height", "properties": {}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 644, "type": "GetNode", "pos": [-4166.40771484375, 2680.19189453125], "size": [210, 58], "flags": {"collapsed": true}, "order": 37, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": []}], "title": "Get_length", "properties": {}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 645, "type": "GetNode", "pos": [-4171.55322265625, 2339.51171875], "size": [210, 58], "flags": {"collapsed": true}, "order": 38, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [1029]}], "title": "Get_pos_prompt", "properties": {}, "widgets_values": ["pos_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 646, "type": "GetNode", "pos": [-4171.36181640625, 2407.369140625], "size": [210, 58], "flags": {"collapsed": true}, "order": 39, "mode": 0, "inputs": [], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [1030]}], "title": "Get_neg_prompt", "properties": {}, "widgets_values": ["neg_prompt"], "color": "#332922", "bgcolor": "#593930"}, {"id": 648, "type": "GetNode", "pos": [-4146.8642578125, 2805.9990234375], "size": [210, 58], "flags": {"collapsed": true}, "order": 40, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [1031, 1038]}], "title": "Get_wan_vae", "properties": {}, "widgets_values": ["wan_vae"], "color": "#322", "bgcolor": "#533"}, {"id": 650, "type": "GetNode", "pos": [-2689.012939453125, 2704.25], "size": [210, 58], "flags": {"collapsed": false}, "order": 41, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1021]}], "title": "Get_seed", "properties": {}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 651, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2951.892822265625, 2294.14892578125], "size": [270, 262], "flags": {}, "order": 161, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 1017}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 1018}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 1019}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 1020}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 1021}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 1022}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [1023]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [50, "fixed", 10, 1, "uni_pc", "simple", 1]}, {"id": 653, "type": "TrimVideoLatent", "pos": [-2910.363525390625, 2653.9541015625], "size": [270, 58], "flags": {"collapsed": false}, "order": 163, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 1023}, {"label": "trim_amount", "name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 1024}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [1037]}], "properties": {"Node name for S&R": "TrimVideoLatent", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [0]}, {"id": 649, "type": "GetNode", "pos": [-4175.125, 2742.28662109375], "size": [210, 58], "flags": {"collapsed": true}, "order": 42, "mode": 0, "inputs": [], "outputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "links": []}], "title": "Get_video_mask", "properties": {}, "widgets_values": ["video_mask"], "color": "#1c5715", "bgcolor": "#1f401b"}, {"id": 467, "type": "PreviewImage", "pos": [-3196.085693359375, 580.0772705078125], "size": [402.1533203125, 415.1760559082031], "flags": {}, "order": 125, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1284}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.42"}, "widgets_values": []}, {"id": 615, "type": "GetNode", "pos": [-4004.102783203125, 1910.33837890625], "size": [210, 58], "flags": {"collapsed": true}, "order": 43, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [970, 995]}], "title": "Get_wan_vae", "properties": {}, "widgets_values": ["wan_vae"], "color": "#322", "bgcolor": "#533"}, {"id": 621, "type": "K<PERSON><PERSON><PERSON>", "pos": [-2612.196533203125, 1457.39501953125], "size": [270, 262], "flags": {}, "order": 138, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 972}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 973}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 974}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 975}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 976}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 977}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [979]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [50, "fixed", 10, 1, "uni_pc", "simple", 1]}, {"id": 608, "type": "GetNode", "pos": [-4006.878173828125, 1387.23095703125], "size": [210, 58], "flags": {"collapsed": true}, "order": 44, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [972]}], "title": "Get_wan_model", "properties": {}, "widgets_values": ["wan_model"], "color": "#223", "bgcolor": "#335"}, {"id": 633, "type": "GetNode", "pos": [-4838.30859375, 1369.66796875], "size": [210, 60], "flags": {}, "order": 45, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1002]}], "title": "Get_raw_video", "properties": {}, "widgets_values": ["raw_video"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 632, "type": "DWPreprocessor", "pos": [-4817.576171875, 1725.41259765625], "size": [315, 222], "flags": {}, "order": 109, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 1003}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1004, 1005]}, {"label": "POSE_KEYPOINT", "name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "properties": {"Node name for S&R": "DWPreprocessor", "widget_ue_connectable": {}}, "widgets_values": ["enable", "enable", "enable", 512, "yolox_l.torchscript.pt", "dw-ll_ucoco_384_bs5.torchscript.pt", "disable"]}, {"id": 637, "type": "GetImageRangeFromBatch", "pos": [-2282.581298828125, 1801.09130859375], "size": [428.4000244140625, 102], "flags": {}, "order": 153, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 1650}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1052, 1285]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 658, "type": "GetImageRangeFromBatch", "pos": [-4833.94873046875, 2406.865234375], "size": [428.4000244140625, 102], "flags": {}, "order": 93, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 1026}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK", "link": 1049}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1043, 1047]}, {"label": "MASK", "name": "MASK", "type": "MASK", "links": [1050]}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 668, "type": "GetNode", "pos": [-4830.20361328125, 2320.24169921875], "size": [210, 58], "flags": {"collapsed": true}, "order": 46, "mode": 0, "inputs": [], "outputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "links": [1049]}], "title": "Get_video_mask", "properties": {}, "widgets_values": ["video_mask"], "color": "#1c5715", "bgcolor": "#1f401b"}, {"id": 657, "type": "GetNode", "pos": [-4842.98583984375, 2178.36083984375], "size": [210, 58], "flags": {}, "order": 47, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1026]}], "title": "Get_video_src", "properties": {}, "widgets_values": ["video_src"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 660, "type": "PreviewImage", "pos": [-4412.75439453125, 2187.44873046875], "size": [210, 246], "flags": {}, "order": 110, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1043}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 468, "type": "ImageResizeKJv2", "pos": [-4370.19384765625, 364.8989562988281], "size": [270, 286], "flags": {}, "order": 98, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 714}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 938}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 939}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [943]}, {"label": "width", "name": "width", "type": "INT", "links": []}, {"label": "height", "name": "height", "type": "INT", "links": []}, {"label": "mask", "name": "mask", "type": "MASK"}], "properties": {"Node name for S&R": "ImageResizeKJv2", "cnr_id": "comfyui-kjnodes", "ver": "1.1.0"}, "widgets_values": [1024, 576, "nearest-exact", "pad", "255,255,255", "center", 2, "cpu"]}, {"id": 634, "type": "GetImageRangeFromBatch", "pos": [-4840.4912109375, 1501.57177734375], "size": [428.4000244140625, 102], "flags": {}, "order": 92, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 1002}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1003]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 635, "type": "PreviewImage", "pos": [-4401.806640625, 1778.211181640625], "size": [210, 246], "flags": {}, "order": 121, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1004}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 449, "type": "INTConstant", "pos": [-5724.02197265625, 42.763973236083984], "size": [298.4490966796875, 58], "flags": {}, "order": 48, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [646]}], "title": "Sampling Steps", "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "5dcda71011870278c35d92ff77a677ed2e538f2d"}, "widgets_values": [8], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 16, "type": "CLIPTextEncode", "pos": [-2695.169189453125, 195.4390411376953], "size": [516.2080078125, 148.2071990966797], "flags": {}, "order": 90, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 211}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [213]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#332922", "bgcolor": "#593930"}, {"id": 601, "type": "ImagePadKJ", "pos": [-3627.662841796875, 556.8198852539062], "size": [270, 310], "flags": {}, "order": 49, "mode": 4, "inputs": [{"label": "image", "name": "image", "type": "IMAGE"}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "target_width", "name": "target_width", "shape": 7, "type": "INT"}, {"label": "target_height", "name": "target_height", "shape": 7, "type": "INT"}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": []}, {"label": "masks", "name": "masks", "type": "MASK"}], "properties": {"Node name for S&R": "ImagePadKJ", "cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "widget_ue_connectable": {}}, "widgets_values": [0, 0, 0, 0, 0, "color", "255,255,255", 512, 512]}, {"id": 600, "type": "RMBG", "pos": [-4011.510498046875, 557.3153076171875], "size": [320, 302], "flags": {}, "order": 115, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 943}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1283, 1284]}, {"label": "MASK", "name": "MASK", "type": "MASK"}, {"label": "MASK_IMAGE", "name": "MASK_IMAGE", "type": "IMAGE"}], "properties": {"Node name for S&R": "RMBG", "cnr_id": "comfyui-rmbg", "ver": "2.4.0", "widget_ue_connectable": {}}, "widgets_values": ["RMBG-2.0", 1, 1024, 0, 0, false, false, "Color", "#ffffff"], "color": "#222e40", "bgcolor": "#364254"}, {"id": 876, "type": "RMBG", "pos": [-2747.974853515625, 1958.400634765625], "size": [320, 302], "flags": {}, "order": 157, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 1285}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1286]}, {"label": "MASK", "name": "MASK", "type": "MASK"}, {"label": "MASK_IMAGE", "name": "MASK_IMAGE", "type": "IMAGE"}], "properties": {"Node name for S&R": "RMBG", "cnr_id": "comfyui-rmbg", "ver": "2.4.0", "widget_ue_connectable": {}}, "widgets_values": ["RMBG-2.0", 1, 1024, 0, 0, false, false, "Color", "#ffffff"], "color": "#222e40", "bgcolor": "#364254"}, {"id": 170, "type": "GetNode", "pos": [-219.19859313964844, -905.234130859375], "size": [210, 60], "flags": {"collapsed": true}, "order": 50, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [216, 217, 963]}], "title": "Get_wan_vae", "properties": {}, "widgets_values": ["wan_vae"], "color": "#322", "bgcolor": "#533"}, {"id": 526, "type": "TrimVideoLatent", "pos": [1478.9488525390625, 1020.6430053710938], "size": [270, 58], "flags": {"collapsed": false}, "order": 162, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 823}, {"label": "trim_amount", "name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 1326}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [821]}], "properties": {"Node name for S&R": "TrimVideoLatent", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [0]}, {"id": 531, "type": "K<PERSON><PERSON><PERSON>", "pos": [1149.401611328125, 747.8600463867188], "size": [270, 262], "flags": {}, "order": 160, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 835}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 1323}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 1324}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 1325}, {"label": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": 839}, {"label": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": 840}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [823]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [50, "fixed", 10, 1, "uni_pc", "simple", 1]}, {"id": 530, "type": "WanVaceToVideo", "pos": [405.44964599609375, 771.701904296875], "size": [270, 254], "flags": {}, "order": 155, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 826}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 827}, {"label": "vae", "name": "vae", "type": "VAE", "link": 828}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 864}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK", "link": 865}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 831}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 832}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 833}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 834}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [1317]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [1318]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": []}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": []}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [832, 480, 81, 1, 1.0000000000000002]}, {"id": 533, "type": "GetNode", "pos": [605.787109375, 1099.25341796875], "size": [210, 58], "flags": {"collapsed": true}, "order": 51, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [822, 828, 1319]}], "title": "Get_wan_vae", "properties": {}, "widgets_values": ["wan_vae"], "color": "#322", "bgcolor": "#533"}, {"id": 535, "type": "GetNode", "pos": [1063.33056640625, 1091.2181396484375], "size": [210, 58], "flags": {"collapsed": true}, "order": 52, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [840]}], "title": "Get_steps", "properties": {}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 527, "type": "GetNode", "pos": [1050.04296875, 1054.5614013671875], "size": [210, 58], "flags": {"collapsed": true}, "order": 53, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [839]}], "title": "Get_seed", "properties": {}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 532, "type": "GetNode", "pos": [98.5055160522461, 886.7750854492188], "size": [210, 58], "flags": {"collapsed": true}, "order": 54, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [831, 1329]}], "title": "Get_img_reference", "properties": {}, "widgets_values": ["img_reference"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 151, "type": "INTConstant", "pos": [-5732.64013671875, -80.60894012451172], "size": [303.8179931640625, 58], "flags": {}, "order": 55, "mode": 0, "inputs": [], "outputs": [{"label": "value", "name": "value", "type": "INT", "links": [194]}], "title": "INT Constant (extend overlap frames)", "properties": {"Node name for S&R": "INTConstant", "cnr_id": "comfyui-kjnodes", "ver": "1.1.1"}, "widgets_values": [16], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 882, "type": "DWPreprocessor", "pos": [769.5325927734375, 1137.1336669921875], "size": [315, 222], "flags": {}, "order": 147, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 1457}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1328]}, {"label": "POSE_KEYPOINT", "name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "properties": {"Node name for S&R": "DWPreprocessor", "widget_ue_connectable": {}}, "widgets_values": ["enable", "enable", "enable", 512, "yolox_l.torchscript.pt", "dw-ll_ucoco_384_bs5.torchscript.pt", "disable"]}, {"id": 602, "type": "DWPreprocessor", "pos": [594.9967041015625, -504.8450927734375], "size": [315, 222], "flags": {}, "order": 113, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 957}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [958]}, {"label": "POSE_KEYPOINT", "name": "POSE_KEYPOINT", "type": "POSE_KEYPOINT"}], "properties": {"Node name for S&R": "DWPreprocessor", "widget_ue_connectable": {}}, "widgets_values": ["enable", "enable", "enable", 512, "yolox_l.torchscript.pt", "dw-ll_ucoco_384_bs5.torchscript.pt", "disable"]}, {"id": 879, "type": "WanVideoVACEStartToEndFrame", "pos": [75.46780395507812, -360.18701171875], "size": [403.1999816894531, 190], "flags": {}, "order": 97, "mode": 0, "inputs": [{"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 1311}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE"}, {"label": "control_images", "name": "control_images", "shape": 7, "type": "IMAGE", "link": 1313}, {"label": "inpaint_mask", "name": "inpaint_mask", "shape": 7, "type": "MASK", "link": 1314}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 1310}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [1315]}, {"label": "masks", "name": "masks", "type": "MASK", "links": [1316]}], "properties": {"Node name for S&R": "WanVideoVACEStartToEndFrame", "widget_ue_connectable": {}}, "widgets_values": [81, 0.5, 0, -1]}, {"id": 606, "type": "GetNode", "pos": [-247.519287109375, -304.9520263671875], "size": [210, 60], "flags": {}, "order": 56, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [954]}], "title": "Get_raw_video", "properties": {}, "widgets_values": ["raw_video"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 604, "type": "GetImageRangeFromBatch", "pos": [110.63648986816406, -560.1574096679688], "size": [428.4000244140625, 102], "flags": {}, "order": 96, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 954}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK"}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 956}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [957]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 473, "type": "GetNode", "pos": [-224.0361328125, -614.3740234375], "size": [210, 58], "flags": {"collapsed": true}, "order": 57, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [698, 959]}], "title": "Get_img_reference", "properties": {}, "widgets_values": ["img_reference"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 30, "type": "TrimVideoLatent", "pos": [1028.7939453125, -479.9447021484375], "size": [270, 58], "flags": {"collapsed": false}, "order": 139, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 36}, {"label": "trim_amount", "name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 1471}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [32]}], "properties": {"Node name for S&R": "TrimVideoLatent", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": [0]}, {"id": 537, "type": "WanVideoVACEStartToEndFrame", "pos": [400.22943115234375, 1181.0059814453125], "size": [329.9634704589844, 190], "flags": {}, "order": 152, "mode": 0, "inputs": [{"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 847}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE"}, {"label": "control_images", "name": "control_images", "shape": 7, "type": "IMAGE", "link": 862}, {"label": "inpaint_mask", "name": "inpaint_mask", "shape": 7, "type": "MASK", "link": 885}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 848}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [864]}, {"label": "masks", "name": "masks", "type": "MASK", "links": [865]}], "properties": {"Node name for S&R": "WanVideoVACEStartToEndFrame", "cnr_id": "ComfyUI-WanVideoWrapper", "aux_id": "MaTeZZ/ComfyUI-WanVideoWrapper-MultiTalk", "ver": "9e8978731b8e653664d10d258eb1792a46f2bf17", "widget_ue_connectable": {}}, "widgets_values": [129, 0.5, 0, -1]}, {"id": 481, "type": "GrowMaskWithBlur", "pos": [-4000.901123046875, -682.9962768554688], "size": [272.7650146484375, 246], "flags": {}, "order": 128, "mode": 0, "inputs": [{"label": "mask", "name": "mask", "type": "MASK", "link": 710}], "outputs": [{"label": "mask", "name": "mask", "type": "MASK", "links": [708, 712]}, {"label": "mask_inverted", "name": "mask_inverted", "type": "MASK"}], "properties": {"Node name for S&R": "GrowMaskWithBlur", "cnr_id": "comfyui-kjnodes", "ver": "3e3a1a8aac61dc4515f6a7da74e026f05a80299f"}, "widgets_values": [40, 0, true, false, 1, 1, 1, true]}, {"id": 476, "type": "GroundingDinoSAMSegment (segment anything)", "pos": [-4468.91796875, -409.8847351074219], "size": [404.1744079589844, 122], "flags": {}, "order": 118, "mode": 0, "inputs": [{"label": "sam_model", "name": "sam_model", "type": "SAM_MODEL", "link": 702}, {"label": "grounding_dino_model", "name": "grounding_dino_model", "type": "GROUNDING_DINO_MODEL", "link": 701}, {"label": "image", "name": "image", "type": "IMAGE", "link": 704}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": []}, {"label": "MASK", "name": "MASK", "type": "MASK", "links": [710]}], "properties": {"Node name for S&R": "GroundingDinoSAMSegment (segment anything)", "cnr_id": "comfyui_segment_anything", "ver": "ab6395596399d5048639cdab7e44ec9fae857a93"}, "widgets_values": ["human,hair,clothes", 0.3]}, {"id": 603, "type": "WanVaceToVideo", "pos": [602.97314453125, -854.7380981445312], "size": [270, 254], "flags": {}, "order": 123, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 946}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 947}, {"label": "vae", "name": "vae", "type": "VAE", "link": 963}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 958}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK"}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 959}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 960}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 961}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 962}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [1013]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [1014]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": [1015]}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": [1471]}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [832, 480, 81, 1, 1.0000000000000002]}, {"id": 881, "type": "WanVaceToVideo", "pos": [784.9664306640625, 766.2093505859375], "size": [270, 254], "flags": {}, "order": 158, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 1317}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 1318}, {"label": "vae", "name": "vae", "type": "VAE", "link": 1319}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 1328}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK"}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 1329}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 1320}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1321}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 1322}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [1323]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [1324]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": [1325]}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": [1326]}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [832, 480, 81, 1, 1.0000000000000002]}, {"id": 32, "type": "VHS_VideoCombine", "pos": [2163.607666015625, -975.947998046875], "size": [654.44384765625, 358], "flags": {}, "order": 154, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 660}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8"}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": false, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "AnimateDiff_00001_zsvri_1753692634.mp4", "workflow": "AnimateDiff_00001.png", "fullpath": "/data/ComfyUI/personal/c1b837c44db245f23e14f1f0e1f44eaa/temp/AnimateDiff_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "temp", "frame_rate": 24}}}}, {"id": 662, "type": "VAEDecode", "pos": [-2393.630126953125, 2462.1259765625], "size": [213.95693969726562, 54.918617248535156], "flags": {"collapsed": false}, "order": 165, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 1037}, {"label": "vae", "name": "vae", "type": "VAE", "link": 1038}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1039]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 617, "type": "VAEDecode", "pos": [-2233.311767578125, 1569.9765625], "size": [213.95693969726562, 54.918617248535156], "flags": {"collapsed": false}, "order": 150, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 969}, {"label": "vae", "name": "vae", "type": "VAE", "link": 970}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1650]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 652, "type": "GetNode", "pos": [-3151.545654296875, 2450.754150390625], "size": [210, 58], "flags": {"collapsed": true}, "order": 58, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1022]}], "title": "Get_steps", "properties": {}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 624, "type": "TrimVideoLatent", "pos": [-2600.218017578125, 1800.3214111328125], "size": [270, 58], "flags": {"collapsed": false}, "order": 145, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 979}, {"label": "trim_amount", "name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 980}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [969]}], "properties": {"Node name for S&R": "TrimVideoLatent", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [0]}, {"id": 620, "type": "GetNode", "pos": [-2940.994140625, 1606.196533203125], "size": [210, 58], "flags": {"collapsed": false}, "order": 59, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [976]}], "title": "Get_seed", "properties": {}, "widgets_values": ["seed"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 622, "type": "GetNode", "pos": [-2905.565185546875, 1527.3201904296875], "size": [210, 58], "flags": {"collapsed": true}, "order": 60, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [977]}], "title": "Get_steps", "properties": {}, "widgets_values": ["steps"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 670, "type": "PreviewImage", "pos": [-1925.3623046875, 1448.7421875], "size": [210, 246], "flags": {}, "order": 156, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1052}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 667, "type": "RepeatImageBatch", "pos": [-4868.76708984375, 2744.63427734375], "size": [315, 58], "flags": {}, "order": 111, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 1047}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1048]}], "properties": {"Node name for S&R": "RepeatImageBatch", "widget_ue_connectable": {}}, "widgets_values": [33]}, {"id": 669, "type": "VHS_DuplicateMasks", "pos": [-4819.64794921875, 2585.7724609375], "size": [214.5947265625, 78], "flags": {}, "order": 112, "mode": 0, "inputs": [{"label": "mask", "name": "mask", "type": "MASK", "link": 1050}], "outputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "links": [1051]}, {"label": "count", "name": "count", "type": "INT"}], "properties": {"Node name for S&R": "VHS_DuplicateMasks", "widget_ue_connectable": {}}, "widgets_values": {"multiply_by": 33}}, {"id": 611, "type": "GetNode", "pos": [-4006.091064453125, 1788.04248046875], "size": [210, 58], "flags": {"collapsed": true}, "order": 61, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": []}], "title": "Get_length", "properties": {}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 630, "type": "WanVaceToVideo", "pos": [-3335.911376953125, 1603.9931640625], "size": [270, 254], "flags": {}, "order": 129, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 993}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 994}, {"label": "vae", "name": "vae", "type": "VAE", "link": 995}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 1006}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK"}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 998}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 999}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1000}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [973]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [974]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": [975]}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": [980]}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [832, 480, 33, 1, 1.0000000000000002]}, {"id": 661, "type": "WanVaceToVideo", "pos": [-3735.367431640625, 2250.4970703125], "size": [270, 254], "flags": {}, "order": 159, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 1029}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 1030}, {"label": "vae", "name": "vae", "type": "VAE", "link": 1031}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 1048}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK", "link": 1051}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 1286}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 1034}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 1035}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [1018]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [1019]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": [1020]}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": [1024]}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [832, 480, 33, 1, 1.0000000000000002]}, {"id": 636, "type": "RepeatImageBatch", "pos": [-4008.1650390625, 1577.4666748046875], "size": [315, 58], "flags": {"collapsed": true}, "order": 122, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 1005}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1006]}], "properties": {"Node name for S&R": "RepeatImageBatch", "widget_ue_connectable": {}}, "widgets_values": [33]}, {"id": 663, "type": "GetImageRangeFromBatch", "pos": [-2360.793212890625, 2648.765625], "size": [428.4000244140625, 102], "flags": {}, "order": 167, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 1039}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1653]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 1064, "type": "SaveImage", "pos": [-1953.0450439453125, 2331.577880859375], "size": [315, 270], "flags": {}, "order": 170, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1653}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}, {"id": 1067, "type": "SetNode", "pos": [-773.8922119140625, -917.2822265625], "size": [210, 60], "flags": {}, "order": 99, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 1655}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_first_start", "properties": {"previousName": "first_start"}, "widgets_values": ["first_start"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 17, "type": "WanVaceToVideo", "pos": [230.0528564453125, -875.7991943359375], "size": [270, 254], "flags": {}, "order": 114, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 214}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 215}, {"label": "vae", "name": "vae", "type": "VAE", "link": 216}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 1315}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK", "link": 1316}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 698}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 218}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 219}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 220}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [946]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [947]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": []}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": []}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": [832, 480, 81, 1, 1.0000000000000002]}, {"id": 605, "type": "SetNode", "pos": [-4105.44091796875, -910.9260864257812], "size": [210, 60], "flags": {}, "order": 119, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 953}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_raw_video", "properties": {"previousName": "raw_video"}, "widgets_values": ["raw_video"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 1068, "type": "Fast Groups Bypasser (rgthree)", "pos": [-5744.58740234375, -1019.5812377929688], "size": [570.6307983398438, 427.932861328125], "flags": {}, "order": 62, "mode": 0, "inputs": [], "outputs": [{"label": "OPT_CONNECTION", "name": "OPT_CONNECTION", "type": "*"}], "properties": {"matchColors": "", "matchTitle": "", "showNav": true, "sort": "position", "customSortAlphabet": "", "toggleRestriction": "default", "widget_ue_connectable": {}}}, {"id": 880, "type": "GetNode", "pos": [-228.37969970703125, -176.56919860839844], "size": [210, 60], "flags": {}, "order": 63, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1311]}], "title": "Get_first_start", "properties": {}, "widgets_values": ["first_start"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 465, "type": "LoadImage", "pos": [-4859.908203125, 436.8866271972656], "size": [389.0305480957031, 569.9116821289062], "flags": {"collapsed": false}, "order": 64, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [714]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.42"}, "widgets_values": ["da877d42dcd228c84b298c1831b610fbb9dc9360616c7a32cd480d64514190fd.png", "image", ""]}, {"id": 1066, "type": "LoadImage", "pos": [-1217.12841796875, -918.1845703125], "size": [315, 314], "flags": {}, "order": 65, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1655]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["bd92d8a0ad9d1c105641fe967cb021dc37b19ae5e826df25752cc20fc1f2ada9.png", "image", ""]}, {"id": 92, "type": "ImageResizeKJv2", "pos": [-4385.5576171875, -975.3673706054688], "size": [270, 286], "flags": {}, "order": 103, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 684}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 185}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 186}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [704, 709, 953]}, {"label": "width", "name": "width", "type": "INT"}, {"label": "height", "name": "height", "type": "INT"}, {"label": "mask", "name": "mask", "type": "MASK"}], "properties": {"Node name for S&R": "ImageResizeKJv2", "cnr_id": "comfyui-kjnodes", "ver": "1.1.0"}, "widgets_values": [512, 512, "nearest-exact", "resize", "0, 0, 0", "center", 2, "cpu"]}, {"id": 607, "type": "<PERSON><PERSON><PERSON><PERSON>_ShowInt", "pos": [-4711.09130859375, -972.5736083984375], "size": [315, 76], "flags": {"collapsed": false}, "order": 104, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "widget": {"name": "INT"}, "link": 964}], "outputs": [], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>_ShowInt", "widget_ue_connectable": {}}, "widgets_values": [0]}, {"id": 1073, "type": "MathExpression|pysssss", "pos": [-854.5689086914062, 693.5780639648438], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 116, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1659}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1660}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1661}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1662]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["(floor((a-b)/(b-c)))-1"]}, {"id": 534, "type": "GetNode", "pos": [-350.6900329589844, 966.3802490234375], "size": [210, 58], "flags": {"collapsed": true}, "order": 66, "mode": 0, "inputs": [], "outputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "links": [883]}], "title": "Get_video_mask", "properties": {}, "widgets_values": ["video_mask"], "color": "#1c5715", "bgcolor": "#1f401b"}, {"id": 519, "type": "GetNode", "pos": [-341.198486328125, 1027.9906005859375], "size": [210, 58], "flags": {"collapsed": true}, "order": 67, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [832, 1320]}], "title": "Get_width", "properties": {}, "widgets_values": ["width"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 520, "type": "GetNode", "pos": [-343.110107421875, 1076.2872314453125], "size": [210, 58], "flags": {"collapsed": true}, "order": 68, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [833, 1321]}], "title": "Get_height", "properties": {}, "widgets_values": ["height"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 521, "type": "GetNode", "pos": [-350.1779479980469, 1128.69482421875], "size": [210, 58], "flags": {"collapsed": true}, "order": 69, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [834, 848, 891, 1322, 1455, 1665, 1699]}], "title": "Get_length", "properties": {}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1072, "type": "GetNode", "pos": [-1076.449951171875, 287.1582946777344], "size": [210, 60], "flags": {}, "order": 70, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1657]}], "title": "Get_video_part1", "properties": {}, "widgets_values": ["video_part1"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 1081, "type": "GetNode", "pos": [-1094.0030517578125, 406.2485656738281], "size": [210, 58], "flags": {}, "order": 71, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1667]}], "title": "Get_video_part1", "properties": {}, "widgets_values": ["video_part1"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 1076, "type": "VHS_GetImageCount", "pos": [-1130.489013671875, 597.9381713867188], "size": [231.7255859375, 26], "flags": {"collapsed": false}, "order": 101, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1658}], "outputs": [{"label": "count", "name": "count", "type": "INT", "links": [1659]}], "properties": {"Node name for S&R": "VHS_GetImageCount", "cnr_id": "comfyui-videohelpersuite", "ver": "1.6.1", "widget_ue_connectable": {}}, "widgets_values": {}}, {"id": 553, "type": "GetImageRangeFromBatch", "pos": [1799.0655517578125, 872.7649536132812], "size": [428.4000244140625, 102], "flags": {}, "order": 166, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 876}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK"}, {"label": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": 880}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 1697}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1674, 1692, 1693]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 1082, "type": "VHS_GetImageCount", "pos": [-504.9662170410156, 1217.8262939453125], "size": [231.7255859375, 26], "flags": {"collapsed": false}, "order": 133, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1668}], "outputs": [{"label": "count", "name": "count", "type": "INT", "links": [1673]}], "properties": {"Node name for S&R": "VHS_GetImageCount", "cnr_id": "comfyui-videohelpersuite", "ver": "1.6.1", "widget_ue_connectable": {}}, "widgets_values": {}}, {"id": 1085, "type": "MathExpression|pysssss", "pos": [1643.4466552734375, 1148.079833984375], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 100, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1699}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1698}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1697]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a-b\n"]}, {"id": 540, "type": "MathExpression|pysssss", "pos": [-449.4025573730469, 1430.190185546875], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 142, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1673}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 844}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [845]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a-b"]}, {"id": 539, "type": "GetImageRangeFromBatch", "pos": [-193.09100341796875, 1232.1104736328125], "size": [428.4000244140625, 102], "flags": {}, "order": 148, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 1672}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK"}, {"label": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": 845}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 846}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [847]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 947, "type": "GetNode", "pos": [479.2474365234375, 1461.1287841796875], "size": [210, 58], "flags": {}, "order": 72, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1454]}], "title": "Get_raw_video", "properties": {}, "widgets_values": ["raw_video"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 948, "type": "GetImageRangeFromBatch", "pos": [686.8202514648438, 1547.1090087890625], "size": [428.4000244140625, 102], "flags": {}, "order": 140, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 1454}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK"}, {"label": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": 1664}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 1455}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1457]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 1080, "type": "MathExpression|pysssss", "pos": [-117.90145874023438, 1635.2603759765625], "size": [241.89715576171875, 128], "flags": {"collapsed": false}, "order": 132, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1665}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1666}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 1670}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1664, 1671]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a + (a - b) * c - b"]}, {"id": 536, "type": "VHS_VideoCombine", "pos": [2293.211669921875, 1074.0123291015625], "size": [654.44384765625, 358], "flags": {}, "order": 168, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1674}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": false, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "AnimateDiff_00006_hyycn_1753693616.mp4", "workflow": "AnimateDiff_00006.png", "fullpath": "/data/ComfyUI/personal/c1b837c44db245f23e14f1f0e1f44eaa/temp/AnimateDiff_00006.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "temp", "frame_rate": 24}}}}, {"id": 1077, "type": "GetNode", "pos": [-1164.24609375, 1116.041748046875], "size": [210, 58], "flags": {"collapsed": false}, "order": 73, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1661]}], "title": "Get_int_overlap_frames", "properties": {}, "widgets_values": ["int_overlap_frames"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1078, "type": "GetNode", "pos": [-1168.738037109375, 948.000244140625], "size": [210, 58], "flags": {"collapsed": false}, "order": 74, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [1660]}], "title": "Get_length", "properties": {}, "widgets_values": ["length"], "color": "#1b4669", "bgcolor": "#29699c"}, {"id": 1075, "type": "GetNode", "pos": [-1177.101806640625, 776.6446533203125], "size": [210, 58], "flags": {"collapsed": false}, "order": 75, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [1658]}], "title": "Get_video_src", "properties": {}, "widgets_values": ["video_src"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 1084, "type": "VHS_VideoCombine", "pos": [4210.68603515625, -1040.8037109375], "size": [654.44384765625, 358], "flags": {}, "order": 172, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 1700}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "AnimateDiff_00005_elhup_1753686853.mp4", "workflow": "AnimateDiff_00005.png", "fullpath": "/data/ComfyUI/personal/c1b837c44db245f23e14f1f0e1f44eaa/output/AnimateDiff_00005.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 24}}}}, {"id": 1079, "type": "<PERSON><PERSON><PERSON><PERSON>_ShowInt", "pos": [-338.27703857421875, 285.6687927246094], "size": [315, 76], "flags": {}, "order": 131, "mode": 0, "inputs": [{"label": "INT", "name": "INT", "type": "INT", "widget": {"name": "INT"}, "link": 1663}], "outputs": [], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>_ShowInt", "widget_ue_connectable": {}}, "widgets_values": [0]}, {"id": 1070, "type": "easy forLoopStart", "pos": [-624.9781494140625, 443.9944152832031], "size": [287.4527587890625, 158], "flags": {}, "order": 126, "mode": 0, "inputs": [{"label": "initial_value1", "name": "initial_value1", "shape": 7, "type": "*", "link": 1657}, {"label": "initial_value2", "name": "initial_value2", "type": "*", "link": 1667}, {"label": "total", "name": "total", "type": "INT", "widget": {"name": "total"}, "link": 1662}, {"label": "initial_value3", "name": "initial_value3", "type": "*"}, {"label": "initial_value4", "name": "initial_value4", "type": "*", "link": null}], "outputs": [{"label": "flow", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "links": [1656]}, {"label": "index", "name": "index", "type": "INT", "links": [1663, 1670]}, {"label": "value1", "name": "value1", "type": "*", "links": [1668, 1672]}, {"label": "value2", "name": "value2", "type": "*", "links": [1694]}, {"label": "value3", "name": "value3", "type": "*"}, {"label": "value4", "name": "value4", "type": "*", "links": null}], "properties": {"Node name for S&R": "easy forLoopStart", "widget_ue_connectable": {}}, "widgets_values": [4], "color": "#223", "bgcolor": "#335"}, {"id": 1071, "type": "easy forLoopEnd", "pos": [2320.640625, 687.7723388671875], "size": [292.20001220703125, 106], "flags": {}, "order": 171, "mode": 0, "inputs": [{"label": "flow", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "link": 1656}, {"label": "initial_value1", "name": "initial_value1", "shape": 7, "type": "*", "link": 1692}, {"label": "initial_value2", "name": "initial_value2", "type": "*", "link": 1695}, {"label": "initial_value3", "name": "initial_value3", "type": "*"}, {"label": "initial_value4", "name": "initial_value4", "type": "*", "link": null}], "outputs": [{"label": "value1", "name": "value1", "type": "*", "links": []}, {"label": "value2", "name": "value2", "type": "*", "links": [1700]}, {"label": "value3", "name": "value3", "type": "*"}, {"label": "value4", "name": "value4", "type": "*", "links": null}], "properties": {"Node name for S&R": "easy forLoopEnd", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 1083, "type": "ImageBatchMulti", "pos": [2315.083251953125, 871.6864624023438], "size": [270, 102], "flags": {}, "order": 169, "mode": 0, "inputs": [{"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 1694}, {"label": "image_2", "name": "image_2", "type": "IMAGE", "link": 1693}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [1695]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "1.1.1", "widget_ue_connectable": {}}, "widgets_values": [2, null]}, {"id": 544, "type": "GetImageRangeFromBatch", "pos": [-86.68441009521484, 924.4411010742188], "size": [428.4000244140625, 102], "flags": {}, "order": 141, "mode": 0, "inputs": [{"label": "images", "name": "images", "shape": 7, "type": "IMAGE", "link": 855}, {"label": "masks", "name": "masks", "shape": 7, "type": "MASK", "link": 883}, {"label": "start_index", "name": "start_index", "type": "INT", "widget": {"name": "start_index"}, "link": 1671}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 891}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [862]}, {"label": "MASK", "name": "MASK", "type": "MASK", "links": [885]}], "properties": {"Node name for S&R": "GetImageRangeFromBatch", "widget_ue_connectable": {}}, "widgets_values": [0, 1]}, {"id": 15, "type": "CLIPTextEncode", "pos": [-2685.4248046875, -110.28888702392578], "size": [512.0262451171875, 185.2405242919922], "flags": {}, "order": 89, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 210}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [212]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": ["一个光头、戴墨镜的黑人保镖在打太极拳"], "color": "#232", "bgcolor": "#353"}, {"id": 51, "type": "PathchSageAttentionKJ", "pos": [-2308.34033203125, -832.42333984375], "size": [315, 58], "flags": {}, "order": 117, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 741}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [672, 738, 766]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "cnr_id": "comfyui-kjnodes", "ver": "c3dc82108a2a86c17094107ead61d63f8c76200e"}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 1086, "type": "Bookmark (rgthree)", "pos": [-3498.77490234375, -987.6897583007812], "size": [210, 62], "flags": {"collapsed": true}, "order": 76, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["1", 1]}, {"id": 49, "type": "TorchCompileModelWanVideo", "pos": [-2284.328125, -681.4569702148438], "size": [315, 178], "flags": {}, "order": 102, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 673}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [674, 741]}], "properties": {"Node name for S&R": "TorchCompileModelWanVideo", "cnr_id": "comfyui-kjnodes", "ver": "c3dc82108a2a86c17094107ead61d63f8c76200e"}, "widgets_values": ["inductor", false, "default", false, 64, false], "color": "#223", "bgcolor": "#335"}, {"id": 488, "type": "<PERSON> Lora <PERSON> (rgthree)", "pos": [-2708.59521484375, -966.79931640625], "size": [340.20001220703125, 270], "flags": {}, "order": 127, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 766}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 767}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [768]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [769]}], "properties": {"Node name for S&R": "<PERSON> Lora <PERSON> (rgthree)", "widget_ue_connectable": {}}, "widgets_values": [1, 1, "emw_Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1, 1, "DetailEnhancerV1_FusionX.safetensors", 1, 1, "Wan14B_RealismBoost_FusionX.safetensors"]}, {"id": 11, "type": "UNETLoader", "pos": [-3192.0439453125, -969.9022827148438], "size": [422.60821533203125, 82], "flags": {}, "order": 77, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [673]}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.34"}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_VACE-FP8_e4m3fn.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 1087, "type": "Bookmark (rgthree)", "pos": [-5309.2666015625, -440.066162109375], "size": [210, 62], "flags": {"collapsed": true}, "order": 78, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["2", 1]}, {"id": 460, "type": "VHS_LoadVideo", "pos": [-4890.85546875, -820.9464111328125], "size": [373.1264953613281, 334], "flags": {"collapsed": false}, "order": 79, "mode": 0, "inputs": [{"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [684]}, {"label": "frame_count", "name": "frame_count", "type": "INT", "links": [964]}, {"label": "audio", "name": "audio", "type": "AUDIO", "links": [685]}, {"label": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO", "links": [686]}], "properties": {"Node name for S&R": "VHS_LoadVideo", "cnr_id": "comfyui-videohelpersuite", "ver": "1.6.1"}, "widgets_values": {"video": "c7e873f6e54c197ba9bde36e351418ba6e15c63b316b56176d0f06477cae42ff.mp4", "force_rate": 24, "force_size": "Disabled", "custom_width": 0, "custom_height": 0, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1, "format": "<PERSON>", "choose video to upload": "image", "videopreview": {"paused": true, "hidden": false, "params": {"custom_height": 0, "filename": "c7e873f6e54c197ba9bde36e351418ba6e15c63b316b56176d0f06477cae42ff.mp4", "force_rate": 24, "custom_width": 0, "select_every_nth": 1, "frame_load_cap": 0, "format": "video/mp4", "skip_first_frames": 0, "type": "input"}}}}, {"id": 1088, "type": "Bookmark (rgthree)", "pos": [-5250.2294921875, 565.0592651367188], "size": [210, 62], "flags": {"collapsed": true}, "order": 80, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["3", 1]}, {"id": 1089, "type": "Bookmark (rgthree)", "pos": [-3053.682373046875, -107.75920104980469], "size": [210, 62], "flags": {"collapsed": true}, "order": 81, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["4", 1]}, {"id": 1090, "type": "Bookmark (rgthree)", "pos": [-2456.67138671875, 1417.56884765625], "size": [210, 62], "flags": {"collapsed": true}, "order": 82, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["5", 1]}, {"id": 1091, "type": "Bookmark (rgthree)", "pos": [-1644.0146484375, -978.7691040039062], "size": [210, 62], "flags": {"collapsed": true}, "order": 83, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["6", 1]}, {"id": 1092, "type": "Bookmark (rgthree)", "pos": [3357.059814453125, -664.1937255859375], "size": [210, 62], "flags": {"collapsed": true}, "order": 84, "mode": 0, "inputs": [], "outputs": [], "title": "🔖", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["7", 0.5]}], "links": [[32, 30, 0, 31, 0, "LATENT"], [36, 24, 0, 30, 0, "LATENT"], [105, 91, 0, 95, 0, "*"], [155, 14, 0, 130, 0, "*"], [169, 19, 0, 138, 0, "*"], [170, 20, 0, 139, 0, "*"], [171, 21, 0, 140, 0, "*"], [185, 85, 0, 92, 1, "INT"], [186, 87, 0, 92, 2, "INT"], [194, 151, 0, 152, 0, "*"], [209, 163, 0, 24, 0, "MODEL"], [210, 164, 0, 15, 0, "CLIP"], [211, 164, 0, 16, 0, "CLIP"], [212, 15, 0, 166, 0, "*"], [213, 16, 0, 167, 0, "*"], [214, 168, 0, 17, 0, "CONDITIONING"], [215, 169, 0, 17, 1, "CONDITIONING"], [216, 170, 0, 17, 2, "VAE"], [217, 170, 0, 31, 1, "VAE"], [218, 172, 0, 17, 6, "INT"], [219, 173, 0, 17, 7, "INT"], [220, 171, 0, 17, 8, "INT"], [224, 31, 0, 176, 0, "*"], [645, 445, 0, 24, 5, "INT"], [646, 449, 0, 446, 0, "*"], [647, 448, 0, 447, 0, "*"], [654, 454, 0, 24, 4, "INT"], [660, 176, 0, 32, 0, "IMAGE"], [671, 27, 0, 129, 0, "MODEL"], [673, 11, 0, 49, 0, "MODEL"], [684, 460, 0, 92, 0, "IMAGE"], [685, 460, 2, 90, 0, "AUDIO"], [686, 460, 3, 91, 0, "VHS_VIDEOINFO"], [698, 473, 0, 17, 5, "IMAGE"], [701, 475, 0, 476, 1, "GROUNDING_DINO_MODEL"], [702, 478, 0, 476, 0, "SAM_MODEL"], [704, 92, 0, 476, 2, "IMAGE"], [707, 480, 0, 479, 1, "IMAGE"], [708, 481, 0, 480, 0, "MASK"], [709, 92, 0, 479, 0, "IMAGE"], [710, 476, 1, 481, 0, "MASK"], [711, 479, 0, 89, 0, "IMAGE"], [712, 481, 0, 363, 0, "MASK"], [714, 465, 0, 468, 0, "IMAGE"], [741, 49, 0, 51, 0, "MODEL"], [766, 51, 0, 488, 0, "MODEL"], [767, 13, 0, 488, 1, "CLIP"], [768, 488, 0, 27, 0, "MODEL"], [769, 488, 1, 131, 0, "CLIP"], [821, 526, 0, 525, 0, "LATENT"], [822, 533, 0, 525, 1, "VAE"], [823, 531, 0, 526, 0, "LATENT"], [826, 522, 0, 530, 0, "CONDITIONING"], [827, 523, 0, 530, 1, "CONDITIONING"], [828, 533, 0, 530, 2, "VAE"], [831, 532, 0, 530, 5, "IMAGE"], [832, 519, 0, 530, 6, "INT"], [833, 520, 0, 530, 7, "INT"], [834, 521, 0, 530, 8, "INT"], [835, 518, 0, 531, 0, "MODEL"], [839, 527, 0, 531, 4, "INT"], [840, 535, 0, 531, 5, "INT"], [844, 541, 0, 540, 1, "INT,FLOAT,IMAGE,LATENT"], [845, 540, 0, 539, 2, "INT"], [846, 541, 0, 539, 3, "INT"], [847, 539, 0, 537, 0, "IMAGE"], [848, 521, 0, 537, 4, "INT"], [855, 529, 0, 544, 0, "IMAGE"], [862, 544, 0, 537, 2, "IMAGE"], [864, 537, 0, 530, 3, "IMAGE"], [865, 537, 1, 530, 4, "MASK"], [876, 525, 0, 553, 0, "IMAGE"], [880, 541, 0, 553, 2, "INT"], [883, 534, 0, 544, 1, "MASK"], [885, 544, 1, 537, 3, "MASK"], [891, 521, 0, 544, 3, "INT"], [938, 588, 0, 468, 2, "INT"], [939, 589, 0, 468, 3, "INT"], [943, 468, 0, 600, 0, "IMAGE"], [946, 17, 0, 603, 0, "CONDITIONING"], [947, 17, 1, 603, 1, "CONDITIONING"], [953, 92, 0, 605, 0, "*"], [954, 606, 0, 604, 0, "IMAGE"], [956, 171, 0, 604, 2, "INT"], [957, 604, 0, 602, 0, "IMAGE"], [958, 602, 0, 603, 3, "IMAGE"], [959, 473, 0, 603, 5, "IMAGE"], [960, 172, 0, 603, 6, "INT"], [961, 173, 0, 603, 7, "INT"], [962, 171, 0, 603, 8, "INT"], [963, 170, 0, 603, 2, "VAE"], [964, 460, 1, 607, 0, "INT"], [969, 624, 0, 617, 0, "LATENT"], [970, 615, 0, 617, 1, "VAE"], [972, 608, 0, 621, 0, "MODEL"], [973, 630, 0, 621, 1, "CONDITIONING"], [974, 630, 1, 621, 2, "CONDITIONING"], [975, 630, 2, 621, 3, "LATENT"], [976, 620, 0, 621, 4, "INT"], [977, 622, 0, 621, 5, "INT"], [979, 621, 0, 624, 0, "LATENT"], [980, 630, 3, 624, 1, "INT"], [993, 612, 0, 630, 0, "CONDITIONING"], [994, 613, 0, 630, 1, "CONDITIONING"], [995, 615, 0, 630, 2, "VAE"], [998, 631, 0, 630, 5, "IMAGE"], [999, 609, 0, 630, 6, "INT"], [1000, 610, 0, 630, 7, "INT"], [1002, 633, 0, 634, 0, "IMAGE"], [1003, 634, 0, 632, 0, "IMAGE"], [1004, 632, 0, 635, 0, "IMAGE"], [1005, 632, 0, 636, 0, "IMAGE"], [1006, 636, 0, 630, 3, "IMAGE"], [1013, 603, 0, 24, 1, "CONDITIONING"], [1014, 603, 1, 24, 2, "CONDITIONING"], [1015, 603, 2, 24, 3, "LATENT"], [1017, 641, 0, 651, 0, "MODEL"], [1018, 661, 0, 651, 1, "CONDITIONING"], [1019, 661, 1, 651, 2, "CONDITIONING"], [1020, 661, 2, 651, 3, "LATENT"], [1021, 650, 0, 651, 4, "INT"], [1022, 652, 0, 651, 5, "INT"], [1023, 651, 0, 653, 0, "LATENT"], [1024, 661, 3, 653, 1, "INT"], [1026, 657, 0, 658, 0, "IMAGE"], [1029, 645, 0, 661, 0, "CONDITIONING"], [1030, 646, 0, 661, 1, "CONDITIONING"], [1031, 648, 0, 661, 2, "VAE"], [1034, 642, 0, 661, 6, "INT"], [1035, 643, 0, 661, 7, "INT"], [1037, 653, 0, 662, 0, "LATENT"], [1038, 648, 0, 662, 1, "VAE"], [1039, 662, 0, 663, 0, "IMAGE"], [1043, 658, 0, 660, 0, "IMAGE"], [1047, 658, 0, 667, 0, "IMAGE"], [1048, 667, 0, 661, 3, "IMAGE"], [1049, 668, 0, 658, 1, "MASK"], [1050, 658, 1, 669, 0, "MASK"], [1051, 669, 0, 661, 4, "MASK"], [1052, 637, 0, 670, 0, "IMAGE"], [1283, 600, 0, 471, 0, "IMAGE"], [1284, 600, 0, 467, 0, "IMAGE"], [1285, 637, 0, 876, 0, "IMAGE"], [1286, 876, 0, 661, 5, "IMAGE"], [1310, 171, 0, 879, 4, "INT"], [1311, 880, 0, 879, 0, "IMAGE"], [1313, 180, 0, 879, 2, "IMAGE"], [1314, 182, 0, 879, 3, "MASK"], [1315, 879, 0, 17, 3, "IMAGE"], [1316, 879, 1, 17, 4, "MASK"], [1317, 530, 0, 881, 0, "CONDITIONING"], [1318, 530, 1, 881, 1, "CONDITIONING"], [1319, 533, 0, 881, 2, "VAE"], [1320, 519, 0, 881, 6, "INT"], [1321, 520, 0, 881, 7, "INT"], [1322, 521, 0, 881, 8, "INT"], [1323, 881, 0, 531, 1, "CONDITIONING"], [1324, 881, 1, 531, 2, "CONDITIONING"], [1325, 881, 2, 531, 3, "LATENT"], [1326, 881, 3, 526, 1, "INT"], [1328, 882, 0, 881, 3, "IMAGE"], [1329, 532, 0, 881, 5, "IMAGE"], [1454, 947, 0, 948, 0, "IMAGE"], [1455, 521, 0, 948, 3, "INT"], [1457, 948, 0, 882, 0, "IMAGE"], [1471, 603, 3, 30, 1, "INT"], [1650, 617, 0, 637, 0, "IMAGE"], [1653, 663, 0, 1064, 0, "IMAGE"], [1655, 1066, 0, 1067, 0, "*"], [1656, 1070, 0, 1071, 0, "FLOW_CONTROL"], [1657, 1072, 0, 1070, 0, "*"], [1658, 1075, 0, 1076, 0, "IMAGE"], [1659, 1076, 0, 1073, 0, "INT,FLOAT,IMAGE,LATENT"], [1660, 1078, 0, 1073, 1, "INT,FLOAT,IMAGE,LATENT"], [1661, 1077, 0, 1073, 2, "INT,FLOAT,IMAGE,LATENT"], [1662, 1073, 0, 1070, 2, "INT"], [1663, 1070, 1, 1079, 0, "INT"], [1664, 1080, 0, 948, 2, "INT"], [1665, 521, 0, 1080, 0, "INT,FLOAT,IMAGE,LATENT"], [1666, 541, 0, 1080, 1, "INT,FLOAT,IMAGE,LATENT"], [1667, 1081, 0, 1070, 1, "*"], [1668, 1070, 2, 1082, 0, "IMAGE"], [1670, 1070, 1, 1080, 2, "INT,FLOAT,IMAGE,LATENT"], [1671, 1080, 0, 544, 2, "INT"], [1672, 1070, 2, 539, 0, "IMAGE"], [1673, 1082, 0, 540, 0, "INT,FLOAT,IMAGE,LATENT"], [1674, 553, 0, 536, 0, "IMAGE"], [1692, 553, 0, 1071, 1, "*"], [1693, 553, 0, 1083, 1, "IMAGE"], [1694, 1070, 3, 1083, 0, "IMAGE"], [1695, 1083, 0, 1071, 2, "*"], [1697, 1085, 0, 553, 3, "INT"], [1698, 541, 0, 1085, 1, "INT,FLOAT,IMAGE,LATENT"], [1699, 521, 0, 1085, 0, "INT,FLOAT,IMAGE,LATENT"], [1700, 1071, 1, 1084, 0, "IMAGE"]], "groups": [{"id": 2, "title": "Prompt", "bounding": [-3216.597900390625, -239.279052734375, 1765.3575439453125, 633.6539916992188], "color": "#b58b2a", "font_size": 24, "flags": {}}, {"id": 4, "title": "第一段 视频生成", "bounding": [-342.8582763671875, -996.950439453125, 2425.132568359375, 994.9173583984375], "color": "#8AA", "font_size": 24, "flags": {}}, {"id": 5, "title": "setting", "bounding": [-5750.12255859375, -517.91015625, 720.8401489257812, 792.3081665039062], "color": "#8A8", "font_size": 24, "flags": {}}, {"id": 6, "title": "模型加载", "bounding": [-3220.091064453125, -1069.4039306640625, 1766.6854248046875, 766.5986938476562], "color": "#88A", "font_size": 24, "flags": {}}, {"id": 11, "title": "参考视频上传", "bounding": [-4924.7353515625, -1052.508056640625, 1568.3623046875, 1207.89990234375], "color": "#a1309b", "font_size": 24, "flags": {}}, {"id": 29, "title": "参考图上传", "bounding": [-4895.005859375, 266.4091796875, 1563.283203125, 716.7724609375], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 41, "title": "生成视频区", "bounding": [-1237.12841796875, -1095.646728515625, 4587.27392578125, 3718.21044921875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 30, "title": "第二段", "bounding": [-363.2077941894531, 640.5360717773438, 2473.97412109375, 1014.8284301757812], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 35, "title": "定帧术", "bounding": [-4890.8876953125, 1219.51025390625, 3235.6220703125, 1664.5889892578125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 33, "title": "定POSE", "bounding": [-4880.8876953125, 1263.1103515625, 3214.72509765625, 767.1898803710938], "color": "#8AA", "font_size": 24, "flags": {}}, {"id": 34, "title": "定背景", "bounding": [-4886.26513671875, 2089.10107421875, 3209.98876953125, 790.0098266601562], "color": "#8AA", "font_size": 24, "flags": {}}, {"id": 40, "title": "上传首帧", "bounding": [-1227.12841796875, -991.7845458984375, 673.2360229492188, 397.6000061035156], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.15772225476662663, "offset": [6569.662682476119, 2654.5452672968418]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false}, "version": 0.4}