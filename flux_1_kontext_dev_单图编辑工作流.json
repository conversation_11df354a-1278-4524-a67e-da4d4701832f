{"id": "7cbcec68-7fa6-47bb-a38a-da689949a001", "revision": 0, "last_node_id": 194, "last_link_id": 302, "nodes": [{"id": 8, "type": "VAEDecode", "pos": [921.4822998046875, -59.39663314819336], "size": [190, 46], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": 52}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 61}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [240]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 35, "type": "FluxGuidance", "pos": [272.5789489746094, -27.9123592376709], "size": [240, 58], "flags": {"collapsed": false}, "order": 10, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 292}, {"localized_name": "guidance", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [57]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxGuidance", "widget_ue_connectable": {}}, "widgets_values": [2.5]}, {"id": 177, "type": "ReferenceLatent", "pos": [22.543743133544922, -48.1579704284668], "size": [197.712890625, 46], "flags": {}, "order": 9, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 294}, {"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 293}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [292]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.41", "Node name for S&R": "ReferenceLatent", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 135, "type": "ConditioningZeroOut", "pos": [267.5614013671875, 110.9385757446289], "size": [240, 26], "flags": {"collapsed": false}, "order": 6, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 237}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [238]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "ConditioningZeroOut", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 37, "type": "UNETLoader", "pos": [-353.58782958984375, -21.60531234741211], "size": [337.76861572265625, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "weight_dtype", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": [58]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "UNETLoader", "models": [{"name": "flux1-dev-kontext_fp8_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/flux1-kontext-dev_ComfyUI/resolve/main/split_files/diffusion_models/flux1-dev-kontext_fp8_scaled.safetensors", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["flux1-dev-kontext_fp8_scaled.safetensors", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 38, "type": "DualCLIPLoader", "pos": [-356.0965576171875, 120.93856811523438], "size": [337.76861572265625, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": [59]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "DualCLIPLoader", "models": [{"name": "clip_l.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/clip_l.safetensors", "directory": "text_encoders"}, {"name": "t5xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/comfyanonymous/flux_text_encoders/resolve/main/t5xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"], "color": "#322", "bgcolor": "#533"}, {"id": 39, "type": "VAELoader", "pos": [-354.8421936035156, 299.6841735839844], "size": [337.76861572265625, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [{"localized_name": "vae_name", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": [61, 223]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "VAELoader", "models": [{"name": "ae.safetensors", "url": "https://huggingface.co/Comfy-Org/Lumina_Image_2.0_Repackaged/resolve/main/split_files/vae/ae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 42, "type": "FluxKontextImageScale", "pos": [54.798309326171875, 342.38592529296875], "size": [270, 30], "flags": {"collapsed": false}, "order": 5, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "IMAGE", "link": 301}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [222, 289]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "FluxKontextImageScale", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 124, "type": "VAEEncode", "pos": [24.85629653930664, 161.78260803222656], "size": [240, 50], "flags": {"collapsed": false}, "order": 7, "mode": 0, "inputs": [{"localized_name": "pixels", "name": "pixels", "type": "IMAGE", "link": 222}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": 223}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "links": [293, 302]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "VAEEncode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 31, "type": "K<PERSON><PERSON><PERSON>", "pos": [545.2631225585938, -99.24037170410156], "size": [320, 576], "flags": {}, "order": 11, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": 58}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": 57}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": 238}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": 302}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": [52]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "K<PERSON><PERSON><PERSON>", "widget_ue_connectable": {}}, "widgets_values": [320364055775726, "randomize", 20, 1, "euler", "simple", 1, ""]}, {"id": 6, "type": "CLIPTextEncode", "pos": [34.61786651611328, 520.0772094726562], "size": [400, 220], "flags": {}, "order": 4, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": 59}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [237, 294]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"cnr_id": "comfy-core", "ver": "0.3.38", "Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["Clear all things from the image, keeping only the Background,", [false, true]], "color": "#232", "bgcolor": "#353"}, {"id": 142, "type": "LoadImageOutput", "pos": [-354.541259765625, 425.2899169921875], "size": [320, 374], "flags": {}, "order": 3, "mode": 0, "inputs": [{"localized_name": "image", "name": "image", "type": "COMBO", "widget": {"name": "image"}, "link": null}, {"localized_name": "choose file to upload", "name": "upload", "type": "IMAGEUPLOAD", "widget": {"name": "upload"}, "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [301]}, {"localized_name": "MASK", "name": "MASK", "type": "MASK", "links": null}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "LoadImageOutput", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI_00222_.png [output]", false, "refresh", "image"], "color": "#322", "bgcolor": "#533"}, {"id": 173, "type": "PreviewImage", "pos": [469.1459655761719, 539.4313354492188], "size": [320.614013671875, 359.5534973144531], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 289}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.40", "Node name for S&R": "PreviewImage", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 136, "type": "SaveImage", "pos": [882.9301147460938, 45.4210090637207], "size": [767.870849609375, 771.9617309570312], "flags": {}, "order": 13, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": 240}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.39", "Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}], "links": [[52, 31, 0, 8, 0, "LATENT"], [57, 35, 0, 31, 1, "CONDITIONING"], [58, 37, 0, 31, 0, "MODEL"], [59, 38, 0, 6, 0, "CLIP"], [61, 39, 0, 8, 1, "VAE"], [222, 42, 0, 124, 0, "IMAGE"], [223, 39, 0, 124, 1, "VAE"], [237, 6, 0, 135, 0, "CONDITIONING"], [238, 135, 0, 31, 2, "CONDITIONING"], [240, 8, 0, 136, 0, "IMAGE"], [289, 42, 0, 173, 0, "IMAGE"], [292, 177, 0, 35, 0, "CONDITIONING"], [293, 124, 0, 177, 1, "LATENT"], [294, 6, 0, 177, 0, "CONDITIONING"], [301, 142, 0, 42, 0, "IMAGE"], [302, 124, 0, 31, 3, "LATENT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.7972024500000039, "offset": [783.066064257356, 153.8581574716593]}, "frontendVersion": "1.23.2", "groupNodes": {"FLUX.1 Kontext Image Edit": {"nodes": [{"id": -1, "type": "Reroute", "pos": [2354.87890625, -127.23468780517578], "size": [75, 26], "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "", "type": "*", "link": null}], "outputs": [{"name": "", "type": "*", "links": null}], "properties": {"showOutputText": false, "horizontal": false}, "index": 0}, {"id": -1, "type": "ReferenceLatent", "pos": [2730, -220], "size": [197.712890625, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": []}], "properties": {"Node name for S&R": "ReferenceLatent", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 1}, {"id": -1, "type": "VAEDecode", "pos": [3270, -110], "size": [210, 46], "flags": {}, "order": 25, "mode": 0, "inputs": [{"localized_name": "samples", "name": "samples", "type": "LATENT", "link": null}, {"localized_name": "vae", "name": "vae", "type": "VAE", "link": null}], "outputs": [{"localized_name": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "index": 2}, {"id": -1, "type": "K<PERSON><PERSON><PERSON>", "pos": [2930, -110], "size": [315, 262], "flags": {}, "order": 24, "mode": 0, "inputs": [{"localized_name": "model", "name": "model", "type": "MODEL", "link": null}, {"localized_name": "positive", "name": "positive", "type": "CONDITIONING", "link": null}, {"localized_name": "negative", "name": "negative", "type": "CONDITIONING", "link": null}, {"localized_name": "latent_image", "name": "latent_image", "type": "LATENT", "link": null}, {"localized_name": "seed", "name": "seed", "type": "INT", "widget": {"name": "seed"}, "link": null}, {"localized_name": "steps", "name": "steps", "type": "INT", "widget": {"name": "steps"}, "link": null}, {"localized_name": "cfg", "name": "cfg", "type": "FLOAT", "widget": {"name": "cfg"}, "link": null}, {"localized_name": "sampler_name", "name": "sampler_name", "type": "COMBO", "widget": {"name": "sampler_name"}, "link": null}, {"localized_name": "scheduler", "name": "scheduler", "type": "COMBO", "widget": {"name": "scheduler"}, "link": null}, {"localized_name": "denoise", "name": "denoise", "type": "FLOAT", "widget": {"name": "denoise"}, "link": null}], "outputs": [{"localized_name": "LATENT", "name": "LATENT", "type": "LATENT", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [972054013131369, "fixed", 20, 1, "euler", "simple", 1], "index": 3}, {"id": -1, "type": "FluxGuidance", "pos": [2940, -220], "size": [211.60000610351562, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"localized_name": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": null}, {"localized_name": "guidance", "name": "guidance", "type": "FLOAT", "widget": {"name": "guidance"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "properties": {"Node name for S&R": "FluxGuidance", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [2.5], "index": 4}, {"id": -1, "type": "SaveImage", "pos": [3490, -110], "size": [985.3012084960938, 1060.3828125], "flags": {}, "order": 26, "mode": 0, "inputs": [{"localized_name": "images", "name": "images", "type": "IMAGE", "link": null}, {"localized_name": "filename_prefix", "name": "filename_prefix", "type": "STRING", "widget": {"name": "filename_prefix"}, "link": null}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ComfyUI"], "index": 5}, {"id": -1, "type": "CLIPTextEncode", "pos": [2500, -110], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 12, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["there is a bright light"], "color": "#232", "bgcolor": "#353", "index": 6}, {"id": -1, "type": "CLIPTextEncode", "pos": [2504.1435546875, 97.9598617553711], "size": [422.84503173828125, 164.31304931640625], "flags": {"collapsed": true}, "order": 13, "mode": 0, "inputs": [{"localized_name": "clip", "name": "clip", "type": "CLIP", "link": null}, {"localized_name": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": null}], "outputs": [{"localized_name": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": []}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": [""], "color": "#322", "bgcolor": "#533", "index": 7}, {"id": -1, "type": "UNETLoader", "pos": [2630, -370], "size": [270, 82], "flags": {}, "order": 6, "mode": 0, "inputs": [{"localized_name": "unet_name", "name": "unet_name", "type": "COMBO", "widget": {"name": "unet_name"}, "link": null}, {"localized_name": "weight_dtype", "name": "weight_dtype", "type": "COMBO", "widget": {"name": "weight_dtype"}, "link": null}], "outputs": [{"localized_name": "MODEL", "name": "MODEL", "type": "MODEL", "links": []}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["flux1-kontext-dev.safetensors", "default"], "color": "#223", "bgcolor": "#335", "index": 8}, {"id": -1, "type": "DualCLIPLoader", "pos": [2100, -290], "size": [337.76861572265625, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [{"localized_name": "clip_name1", "name": "clip_name1", "type": "COMBO", "widget": {"name": "clip_name1"}, "link": null}, {"localized_name": "clip_name2", "name": "clip_name2", "type": "COMBO", "widget": {"name": "clip_name2"}, "link": null}, {"localized_name": "type", "name": "type", "type": "COMBO", "widget": {"name": "type"}, "link": null}, {"localized_name": "device", "name": "device", "shape": 7, "type": "COMBO", "widget": {"name": "device"}, "link": null}], "outputs": [{"localized_name": "CLIP", "name": "CLIP", "type": "CLIP", "links": []}], "properties": {"Node name for S&R": "DualCLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn_scaled.safetensors", "flux", "default"], "color": "#223", "bgcolor": "#335", "index": 9}, {"id": -1, "type": "VAELoader", "pos": [2960, -370], "size": [270, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [{"localized_name": "vae_name", "name": "vae_name", "type": "COMBO", "widget": {"name": "vae_name"}, "link": null}], "outputs": [{"localized_name": "VAE", "name": "VAE", "type": "VAE", "links": []}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.38"}, "widgets_values": ["ae.safetensors"], "color": "#223", "bgcolor": "#335", "index": 10}], "links": [[6, 0, 1, 0, 72, "CONDITIONING"], [0, 0, 1, 1, 66, "*"], [3, 0, 2, 0, 69, "LATENT"], [10, 0, 2, 1, 76, "VAE"], [8, 0, 3, 0, 74, "MODEL"], [4, 0, 3, 1, 70, "CONDITIONING"], [7, 0, 3, 2, 73, "CONDITIONING"], [0, 0, 3, 3, 66, "*"], [1, 0, 4, 0, 67, "CONDITIONING"], [2, 0, 5, 0, 68, "IMAGE"], [9, 0, 6, 0, 75, "CLIP"], [9, 0, 7, 0, 75, "CLIP"]], "external": [], "config": {"0": {}, "1": {}, "2": {"output": {"0": {"visible": true}}}, "3": {"output": {"0": {"visible": true}}, "input": {"denoise": {"visible": false}, "cfg": {"visible": false}}}, "4": {}, "5": {}, "6": {}, "7": {"input": {"text": {"visible": false}}}, "8": {"input": {"weight_dtype": {"visible": false}}}, "9": {"input": {"type": {"visible": false}, "device": {"visible": false}}}, "10": {}}}}, "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true, "ue_links": [], "links_added_by_ue": []}, "version": 0.4}