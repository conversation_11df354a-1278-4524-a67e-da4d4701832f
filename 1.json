{"last_link_id": 369, "last_node_id": 254, "nodes": [{"outputs": [{"shape": 6, "name": "STRING", "label": "STRING", "type": "STRING"}], "color": "#223", "widgets_values": ["Remove women's clothes", "Remove women's clothes"], "inputs": [{"name": "text", "link": 318, "label": "text", "type": "STRING"}], "flags": {"pinned": true}, "type": "ShowText|pysssss", "mode": 0, "bgcolor": "#335", "size": [384.5976867675781, 200.37255859375], "pos": [3388.808837890625, 417.0299987792969], "id": 201, "properties": {"Node name for S&R": "ShowText|pysssss"}, "order": 10}, {"outputs": [{"name": "CLIP", "links": [319], "label": "CLIP", "type": "CLIP"}], "color": "#322", "widgets_values": ["clip_l.safetensors", "t5xxl_fp8_e4m3fn.safetensors", "flux", "default"], "inputs": [], "flags": {"pinned": true}, "type": "DualCLIPLoader", "mode": 0, "bgcolor": "#533", "size": [337.76861572265625, 130], "pos": [2476.27587890625, 814.2345581054688], "id": 202, "properties": {"Node name for S&R": "DualCLIPLoader"}, "order": 0}, {"outputs": [{"name": "VAE", "links": [324, 334], "label": "VAE", "type": "VAE"}], "color": "#322", "widgets_values": ["ae.safetensors"], "inputs": [], "flags": {"pinned": true}, "type": "VAELoader", "mode": 0, "bgcolor": "#533", "size": [337.76861572265625, 58], "pos": [2392.010009765625, 1015.8859252929688], "id": 203, "properties": {"Node name for S&R": "VAELoader"}, "order": 1}, {"outputs": [{"name": "CONDITIONING", "slot_index": 0, "links": [321, 335], "label": "CONDITIONING", "type": "CONDITIONING"}], "color": "#232", "widgets_values": ["Using this elegant style, create a portrait of a swan wearing a pearl tiara and lace collar, maintaining the same refined quality and soft color tones."], "inputs": [{"name": "clip", "link": 319, "label": "clip", "type": "CLIP"}, {"widget": {"name": "text"}, "name": "text", "link": 320, "label": "text", "type": "STRING"}], "flags": {}, "type": "CLIPTextEncode", "mode": 0, "bgcolor": "#353", "size": [385.7064514160156, 93], "pos": [3377.654296875, 986.7821655273438], "id": 204, "properties": {"Node name for S&R": "CLIPTextEncode"}, "order": 12}, {"outputs": [{"name": "CONDITIONING", "links": [341], "label": "CONDITIONING", "type": "CONDITIONING"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "conditioning", "link": 321, "label": "conditioning", "type": "CONDITIONING"}], "flags": {}, "type": "ConditioningZeroOut", "mode": 0, "bgcolor": "#335", "size": [240, 26], "pos": [3659.54150390625, 858.6183471679688], "id": 205, "properties": {"Node name for S&R": "ConditioningZeroOut"}, "order": 16}, {"outputs": [], "color": "#223", "widgets_values": ["T8star-Aix-Remove"], "inputs": [{"name": "images", "link": 322, "label": "images", "type": "IMAGE"}], "flags": {"pinned": true}, "type": "SaveImage", "mode": 4, "bgcolor": "#335", "size": [407.2988586425781, 299.3293762207031], "pos": [4873.939453125, 290.1796569824219], "id": 206, "properties": {"Node name for S&R": "SaveImage"}, "order": 21}, {"outputs": [{"name": "IMAGE", "slot_index": 0, "links": [322], "label": "IMAGE", "type": "IMAGE"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "samples", "link": 323, "label": "samples", "type": "LATENT"}, {"name": "vae", "link": 324, "label": "vae", "type": "VAE"}], "flags": {"pinned": true}, "type": "VAEDecode", "mode": 0, "bgcolor": "#335", "size": [190, 46], "pos": [4022.74462890625, 600.2445678710938], "id": 207, "properties": {"Node name for S&R": "VAEDecode"}, "order": 20}, {"outputs": [{"name": "STRING", "links": [366], "label": "STRING", "type": "STRING"}], "color": "#223", "widgets_values": ["移除女人的衣服"], "inputs": [], "flags": {"pinned": true}, "type": "Text Multiline", "mode": 0, "bgcolor": "#335", "size": [375.3995361328125, 206.6213836669922], "pos": [2488.807861328125, 357.5431823730469], "id": 209, "properties": {"Node name for S&R": "Text Multiline"}, "order": 2}, {"outputs": [{"name": "IMAGE", "links": [359], "label": "IMAGE", "type": "IMAGE"}, {"name": "MASK", "label": "MASK", "type": "MASK"}], "color": "#322", "widgets_values": ["example.png", "image", ""], "inputs": [], "flags": {}, "type": "LoadImage", "mode": 0, "bgcolor": "#533", "size": [340, 350], "pos": [2025.489990234375, -71.45658111572266], "id": 212, "properties": {"Node name for S&R": "LoadImage"}, "order": 3}, {"outputs": [{"name": "MODEL", "links": [368], "label": "MODEL", "type": "MODEL"}], "color": "#223", "widgets_values": [7, 7], "inputs": [{"name": "model", "link": 329, "label": "model", "type": "MODEL"}], "flags": {}, "type": "MZ_Flux1PartialLoad_Patch", "mode": 4, "bgcolor": "#335", "size": [330.2437438964844, 82], "pos": [3202.20361328125, 1172.1201171875], "id": 213, "properties": {"Node name for S&R": "MZ_Flux1PartialLoad_Patch"}, "order": 17}, {"outputs": [{"name": "LATENT", "links": [336, 342], "label": "LATENT", "type": "LATENT"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "pixels", "link": 333, "label": "pixels", "type": "IMAGE"}, {"name": "vae", "link": 334, "label": "vae", "type": "VAE"}], "flags": {}, "type": "VAEEncode", "mode": 0, "bgcolor": "#335", "size": [240, 50], "pos": [3381.999267578125, 857.6554565429688], "id": 217, "properties": {"Node name for S&R": "VAEEncode"}, "order": 14}, {"outputs": [{"name": "CONDITIONING", "links": [337], "label": "CONDITIONING", "type": "CONDITIONING"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "conditioning", "link": 335, "label": "conditioning", "type": "CONDITIONING"}, {"shape": 7, "name": "latent", "link": 336, "label": "latent", "type": "LATENT"}], "flags": {}, "type": "ReferenceLatent", "mode": 0, "bgcolor": "#335", "size": [211.60000610351562, 46], "pos": [3409.************, 728.6439819335938], "id": 218, "properties": {"Node name for S&R": "ReferenceLatent"}, "order": 13}, {"outputs": [{"name": "CONDITIONING", "slot_index": 0, "links": [340], "label": "CONDITIONING", "type": "CONDITIONING"}], "color": "#223", "widgets_values": [10], "inputs": [{"name": "conditioning", "link": 337, "label": "conditioning", "type": "CONDITIONING"}], "flags": {}, "type": "FluxGuidance", "mode": 0, "bgcolor": "#335", "size": [240, 58], "pos": [3702.************, 718.5477905273438], "id": 222, "properties": {"Node name for S&R": "FluxGuidance"}, "order": 15}, {"outputs": [{"name": "LATENT", "slot_index": 0, "links": [323], "label": "LATENT", "type": "LATENT"}], "color": "#223", "widgets_values": [877255100689250, "fixed", 20, 1, "euler", "simple", 1], "inputs": [{"name": "model", "link": 339, "label": "model", "type": "MODEL"}, {"name": "positive", "link": 340, "label": "positive", "type": "CONDITIONING"}, {"name": "negative", "link": 341, "label": "negative", "type": "CONDITIONING"}, {"name": "latent_image", "link": 342, "label": "latent_image", "type": "LATENT"}], "flags": {}, "type": "K<PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#335", "size": [329.6587829589844, 474.0001220703125], "pos": [4431.8115234375, -139.7356414794922], "id": 225, "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "order": 19}, {"outputs": [{"name": "IMAGE", "links": [333], "label": "IMAGE", "type": "IMAGE"}], "color": "#223", "widgets_values": [], "inputs": [{"name": "image", "link": 359, "label": "image", "type": "IMAGE"}], "flags": {}, "type": "FluxKontextImageScale", "mode": 0, "bgcolor": "#335", "size": [314.1544189453125, 28.620174407958984], "pos": [2963.603271484375, 861.8490600585938], "id": 244, "properties": {"Node name for S&R": "FluxKontextImageScale"}, "order": 11}, {"outputs": [{"name": "MODEL", "links": [329], "label": "MODEL", "type": "MODEL"}], "color": "#223", "widgets_values": ["clothes_remover_v0.safetensors", 1], "inputs": [{"name": "model", "link": 360, "label": "model", "type": "MODEL"}], "flags": {}, "type": "LoraLoaderModelOnly", "mode": 0, "bgcolor": "#335", "size": [270, 82], "pos": [2785.21484375, 1169.59521484375], "id": 245, "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "order": 6}, {"outputs": [{"name": "MODEL", "links": [369], "label": "MODEL", "type": "MODEL"}], "color": "#223", "widgets_values": ["flux1-kontext-dev-Q8_0.gguf"], "inputs": [], "flags": {}, "type": "UnetLoaderGGUF", "mode": 0, "bgcolor": "#335", "size": [270, 58], "pos": [2121.93505859375, 1434.637451171875], "id": 247, "properties": {"Node name for S&R": "UnetLoaderGGUF"}, "order": 4}, {"outputs": [{"name": "text", "links": [318, 320], "label": "text", "type": "STRING"}], "color": "#223", "widgets_values": ["zh-cn", "en", false, "Manual Trasnlate", ""], "inputs": [{"widget": {"name": "text"}, "name": "text", "link": 366, "label": "text", "type": "STRING"}], "flags": {}, "type": "GoogleTranslateTextNode", "mode": 0, "bgcolor": "#335", "size": [400, 200], "pos": [2933.392822265625, 402.2062683105469], "id": 251, "properties": {"Node name for S&R": "GoogleTranslateTextNode"}, "order": 9}, {"outputs": [{"name": "MODEL", "links": [339], "label": "MODEL", "type": "MODEL"}], "color": "#223", "widgets_values": ["HyperSD-Accelerator-FLUX-PAseerV2.safetensors", 1], "inputs": [{"name": "model", "link": 368, "label": "model", "type": "MODEL"}], "flags": {}, "type": "LoraLoaderModelOnly", "mode": 4, "bgcolor": "#335", "size": [270, 82], "pos": [3586.958740234375, 1192.1201171875], "id": 253, "properties": {"Node name for S&R": "LoraLoaderModelOnly"}, "order": 18}, {"outputs": [{"name": "model", "links": [360], "label": "model", "type": "MODEL"}], "color": "#322", "shape": 1, "widgets_values": ["flux", 0.2, 0.1, 2], "inputs": [{"name": "model", "link": 369, "label": "model", "type": "MODEL"}], "flags": {}, "type": "<PERSON><PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#533", "size": [315.3089294433594, 178], "pos": [2489.519775390625, 1298.010986328125], "id": 254, "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON>"}, "order": 5}], "links": [[318, 251, 0, 201, 0, "STRING"], [319, 202, 0, 204, 0, "CLIP"], [320, 251, 0, 204, 1, "STRING"], [321, 204, 0, 205, 0, "CONDITIONING"], [322, 207, 0, 206, 0, "IMAGE"], [323, 225, 0, 207, 0, "LATENT"], [324, 203, 0, 207, 1, "VAE"], [329, 245, 0, 213, 0, "MODEL"], [333, 244, 0, 217, 0, "IMAGE"], [334, 203, 0, 217, 1, "VAE"], [335, 204, 0, 218, 0, "CONDITIONING"], [336, 217, 0, 218, 1, "LATENT"], [337, 218, 0, 222, 0, "CONDITIONING"], [339, 253, 0, 225, 0, "MODEL"], [340, 222, 0, 225, 1, "CONDITIONING"], [341, 205, 0, 225, 2, "CONDITIONING"], [342, 217, 0, 225, 3, "LATENT"], [359, 212, 0, 244, 0, "IMAGE"], [360, 254, 0, 245, 0, "MODEL"], [366, 209, 0, 251, 0, "STRING"], [368, 213, 0, 253, 0, "MODEL"], [369, 247, 0, 254, 0, "MODEL"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}