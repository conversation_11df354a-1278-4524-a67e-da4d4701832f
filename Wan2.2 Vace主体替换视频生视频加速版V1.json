{"id": "96995b8f-85c5-47af-b3cf-7b6a24675694", "revision": 0, "last_node_id": 278, "last_link_id": 426, "nodes": [{"id": 7, "type": "CLIPTextEncode", "pos": [-80, 390], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 393}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [97]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.34", "hasSecondTab": false, "widget_ue_connectable": {}, "secondTabText": "Send Back", "enableTabs": false, "secondTabOffset": 80, "tabWidth": 65, "secondTabWidth": 65, "tabXOffset": 10}, "widgets_values": ["过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走,过曝，"], "color": "#322", "bgcolor": "#533"}, {"id": 222, "type": "<PERSON>downNote", "pos": [-860.3633422851562, 1088.7698974609375], "size": [290, 110], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "title": "About reference image", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["Using a solid-colored background for reference images works better."], "color": "#432", "bgcolor": "#653"}, {"id": 229, "type": "PathchSageAttentionKJ", "pos": [44.34646224975586, -1583.505615234375], "size": [315, 58], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 355}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [359]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 230, "type": "UnetLoaderGGUF", "pos": [-754.9332275390625, -1898.1651611328125], "size": [315, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [356]}], "properties": {"Node name for S&R": "UnetLoaderGGUF", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.2_T2V_High_Noise_14B_VACE-Q8_0.gguf"]}, {"id": 231, "type": "PathchSageAttentionKJ", "pos": [-381.61199951171875, -2045.47265625], "size": [315, 58], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 356}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [357]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 232, "type": "LoraLoaderModelOnly", "pos": [57.986846923828125, -2048.380615234375], "size": [315, 82], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 357}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [360]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1]}, {"id": 233, "type": "LoraLoaderModelOnly", "pos": [-276.256591796875, -1753.466064453125], "size": [315, 82], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 358}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [355]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1]}, {"id": 234, "type": "UnetLoaderGGUF", "pos": [-713.7234497070312, -1738.585693359375], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [358]}], "properties": {"Node name for S&R": "UnetLoaderGGUF", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.2_T2V_Low_Noise_14B_VACE-Q8_0.gguf"]}, {"id": 235, "type": "ModelSamplingSD3", "pos": [411.1307373046875, -1501.2132568359375], "size": [315, 58], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 359}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [362]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002]}, {"id": 236, "type": "ModelSamplingSD3", "pos": [549.425048828125, -2019.5672607421875], "size": [315, 58], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 360}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [365]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002]}, {"id": 237, "type": "PrimitiveNode", "pos": [303.4612731933594, -1385.9913330078125], "size": [210, 82], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "widget": {"name": "noise_seed"}, "links": [364, 366]}], "title": "noise_seed", "properties": {"Run widget replace on values": false}, "widgets_values": [714494646738536, "randomize"]}, {"id": 238, "type": "VHS_VideoCombine", "pos": [2582.404296875, -1940.9822998046875], "size": [537.25732421875, 358], "flags": {}, "order": 50, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 406}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "T8", "format": "video/h264-mp4.json", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "T8_00006_soxgz_1753767878.mp4", "workflow": "T8_00006.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/T8_00006.mp4", "format": "video/h264-mp4.json", "subfolder": "", "type": "output", "frame_rate": 16}}}}, {"id": 239, "type": "KSamplerAdvanced", "pos": [1466.135498046875, -1914.1695556640625], "size": [304.748046875, 334], "flags": {}, "order": 46, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 362}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 372}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 373}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 363}, {"label": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": 364}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [367]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 714494646738536, "randomize", 8, 1, "lcm", "simple", 4, 10000, "disable"]}, {"id": 240, "type": "KSamplerAdvanced", "pos": [1037.0311279296875, -1867.3359375], "size": [304.748046875, 334], "flags": {}, "order": 45, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 365}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 369}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 370}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 371}, {"label": "noise_seed", "name": "noise_seed", "type": "INT", "widget": {"name": "noise_seed"}, "link": 366}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [363]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["enable", 714494646738536, "randomize", 8, 1, "lcm", "simple", 0, 4, "enable"]}, {"id": 219, "type": "InvertMask", "pos": [395.2713928222656, 986.4534912109375], "size": [140, 26], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "mask", "name": "mask", "type": "MASK", "link": 390}], "outputs": [{"label": "MASK", "name": "MASK", "type": "MASK", "links": [352]}], "properties": {"Node name for S&R": "InvertMask", "cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": []}, {"id": 249, "type": "LayerMask: SAM2VideoUltra", "pos": [925.6439208984375, 1628.510498046875], "size": [315, 406], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 380}, {"label": "bboxes", "name": "bboxes", "shape": 7, "type": "BBOXES", "link": 381}, {"label": "first_frame_mask", "name": "first_frame_mask", "shape": 7, "type": "MASK"}, {"label": "pre_mask", "name": "pre_mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "mask", "name": "mask", "type": "MASK", "links": [382]}, {"label": "preview", "name": "preview", "type": "IMAGE", "links": []}], "properties": {"Node name for S&R": "LayerMask: SAM2VideoUltra", "widget_ue_connectable": {}}, "widgets_values": ["sam2.1_hiera_base_plus.safetensors", "fp16", false, false, "#FF0080", "VITMatte", 6, 4, 0.15, 0.99, true, "cuda", 0.5], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 244, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [-1743.7110595703125, 1421.597900390625], "size": [504, 330], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 401}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 403}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [376, 377, 383, 384]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT", "links": []}, {"label": "height", "name": "height", "type": "INT", "links": []}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2", "widget_ue_connectable": {}}, "widgets_values": ["original", 1, 1, "crop", "lanc<PERSON>s", "16", "longest", 720, "#000000"], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 250, "type": "LayerMask: SAM2VideoUltra", "pos": [1475.9674072265625, 1610.0191650390625], "size": [315, 406], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 384}, {"label": "bboxes", "name": "bboxes", "shape": 7, "type": "BBOXES", "link": 385}, {"label": "first_frame_mask", "name": "first_frame_mask", "shape": 7, "type": "MASK", "link": 382}, {"label": "pre_mask", "name": "pre_mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "mask", "name": "mask", "type": "MASK", "links": [387]}, {"label": "preview", "name": "preview", "type": "IMAGE"}], "properties": {"Node name for S&R": "LayerMask: SAM2VideoUltra", "widget_ue_connectable": {}}, "widgets_values": ["sam2.1_hiera_base_plus.safetensors", "fp16", false, false, "#FF0080", "VITMatte", 6, 4, 0.15, 0.99, true, "cuda", 0.5], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 252, "type": "GrowMaskWithBlur", "pos": [1176.6627197265625, 891.5003662109375], "size": [315, 246], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "mask", "name": "mask", "type": "MASK", "link": 387}, {"label": "expand", "name": "expand", "type": "INT", "widget": {"name": "expand"}, "link": 386}], "outputs": [{"label": "mask", "name": "mask", "type": "MASK", "slot_index": 0, "links": [388, 389, 390]}, {"label": "mask_inverted", "name": "mask_inverted", "type": "MASK", "slot_index": 1, "links": []}], "properties": {"Node name for S&R": "GrowMaskWithBlur", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [50, 0, true, false, 0, 1, 1, false], "color": "#223", "bgcolor": "#335"}, {"id": 216, "type": "MaskToImage", "pos": [549.6332397460938, 996.9110107421875], "size": [184.62362670898438, 26], "flags": {}, "order": 41, "mode": 0, "inputs": [{"label": "mask", "name": "mask", "type": "MASK", "link": 352}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [334]}], "properties": {"Node name for S&R": "MaskToImage", "cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": []}, {"id": 253, "type": "VAELoader", "pos": [-719.89111328125, 158.3129119873047], "size": [350, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [394, 395]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.34", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 246, "type": "ImageFromBatch+", "pos": [-587.2640991210938, 1536.0257568359375], "size": [315, 82], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 383}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [378, 380]}], "properties": {"Node name for S&R": "ImageFromBatch+", "cnr_id": "comfyui_essentials", "ver": "33ff89fd354d8ec3ab6affb605a79a931b445d99", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [1, 1], "color": "#223", "bgcolor": "#335"}, {"id": 213, "type": "RebatchImages", "pos": [181.88894653320312, 655.4617309570312], "size": [230, 60], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 377}, {"label": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": 340}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "shape": 6, "type": "IMAGE", "links": [333]}], "properties": {"Node name for S&R": "RebatchImages", "cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": [1]}, {"id": 208, "type": "ImageCompositeMasked", "pos": [471.3394470214844, 643.5098876953125], "size": [230, 146], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "destination", "name": "destination", "type": "IMAGE", "link": 333}, {"label": "source", "name": "source", "type": "IMAGE", "link": 334}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK", "link": 389}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [341, 344]}], "properties": {"Node name for S&R": "ImageCompositeMasked", "cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": [0, 0, true]}, {"id": 255, "type": "JWInteger", "pos": [-2705.76904296875, 808.6802978515625], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [402]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [33]}, {"id": 211, "type": "GetImageSize", "pos": [-205.34336853027344, 759.308837890625], "size": [200, 124], "flags": {"collapsed": false}, "order": 31, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 376}], "outputs": [{"label": "width", "name": "width", "type": "INT", "links": [397]}, {"label": "height", "name": "height", "type": "INT", "links": [398]}, {"label": "batch_size", "name": "batch_size", "type": "INT", "links": [340, 399]}], "properties": {"Node name for S&R": "GetImageSize", "cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": []}, {"id": 214, "type": "PreviewImage", "pos": [764.3270874023438, 709.904541015625], "size": [300, 300], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 341}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.40"}, "widgets_values": []}, {"id": 251, "type": "JWInteger", "pos": [-2562.91015625, 1280.1490478515625], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [386]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [1]}, {"id": 241, "type": "TrimVideoLatent", "pos": [1339.076171875, -1132.99169921875], "size": [315, 60], "flags": {"collapsed": false}, "order": 47, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 367}, {"label": "trim_amount", "name": "trim_amount", "type": "INT", "widget": {"name": "trim_amount"}, "link": 404}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [368]}], "properties": {"Node name for S&R": "TrimVideoLatent", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": [0]}, {"id": 242, "type": "VAEDecode", "pos": [1762.692626953125, -1034.982177734375], "size": [315, 46], "flags": {"collapsed": false}, "order": 48, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 368}, {"label": "vae", "name": "vae", "type": "VAE", "link": 395}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [405]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.34", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 257, "type": "VHS_LoadVideo", "pos": [-2025.499267578125, 561.63037109375], "size": [626.64697265625, 690.0828857421875], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"label": "frame_load_cap", "name": "frame_load_cap", "type": "INT", "widget": {"name": "frame_load_cap"}, "link": 402}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [401, 407]}, {"label": "frame_count", "name": "frame_count", "type": "INT", "slot_index": 1, "links": []}, {"label": "audio", "name": "audio", "type": "AUDIO"}, {"label": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO", "slot_index": 3, "links": []}], "properties": {"Node name for S&R": "VHS_LoadVideo", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": {"video": "ec6402c12bb67869e4b7d38cfad9d10543c4d4dec17e7899e149d8c80e3b6932.mp4", "force_rate": 0, "force_size": "Disabled", "custom_width": 0, "custom_height": 0, "frame_load_cap": 33, "skip_first_frames": 0, "select_every_nth": 1, "format": "AnimateDiff", "choose video to upload": "image", "videopreview": {"paused": false, "hidden": false, "params": {"custom_height": 0, "filename": "ec6402c12bb67869e4b7d38cfad9d10543c4d4dec17e7899e149d8c80e3b6932.mp4", "force_rate": 0, "custom_width": 0, "select_every_nth": 1, "frame_load_cap": 33, "format": "video/mp4", "skip_first_frames": 0, "type": "input"}}}, "color": "#223", "bgcolor": "#335"}, {"id": 259, "type": "ImageFromBatch+", "pos": [2199.668701171875, -1145.133056640625], "size": [315, 82], "flags": {}, "order": 49, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 405}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [406, 412]}], "properties": {"Node name for S&R": "ImageFromBatch+", "cnr_id": "comfyui_essentials", "ver": "33ff89fd354d8ec3ab6affb605a79a931b445d99", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [3, -1], "color": "#223", "bgcolor": "#335"}, {"id": 261, "type": "ImageConcanate", "pos": [2747.287353515625, -717.9761962890625], "size": [315, 102], "flags": {}, "order": 51, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 411}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 412}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [413]}], "properties": {"Node name for S&R": "ImageConcanate", "widget_ue_connectable": {}}, "widgets_values": ["right", true]}, {"id": 264, "type": "PDIMAGE_LongerSize", "pos": [3347.546630859375, -1157.5062255859375], "size": [315, 82], "flags": {}, "order": 52, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 413}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [414]}], "properties": {"Node name for S&R": "PDIMAGE_LongerSize", "widget_ue_connectable": {}}, "widgets_values": [1024, "bicubic"]}, {"id": 49, "type": "WanVaceToVideo", "pos": [648.0582275390625, 174.78485107421875], "size": [315, 254], "flags": {}, "order": 44, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 96}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 97}, {"label": "vae", "name": "vae", "type": "VAE", "link": 394}, {"label": "control_video", "name": "control_video", "shape": 7, "type": "IMAGE", "link": 344}, {"label": "control_masks", "name": "control_masks", "shape": 7, "type": "MASK", "link": 388}, {"label": "reference_image", "name": "reference_image", "shape": 7, "type": "IMAGE", "link": 417}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 397}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 399}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 398}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "links": [369, 372]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "links": [370, 373]}, {"label": "latent", "name": "latent", "type": "LATENT", "links": [371]}, {"label": "trim_latent", "name": "trim_latent", "type": "INT", "links": [404]}], "properties": {"Node name for S&R": "WanVaceToVideo", "cnr_id": "comfy-core", "ver": "0.3.34", "hasSecondTab": false, "widget_ue_connectable": {"width": true, "length": true, "height": true}, "secondTabText": "Send Back", "enableTabs": false, "secondTabOffset": 80, "tabWidth": 65, "secondTabWidth": 65, "tabXOffset": 10}, "widgets_values": [720, 720, 81, 1, 1]}, {"id": 266, "type": "RebatchImages", "pos": [-148.3999481201172, 601.7613525390625], "size": [230, 60], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 425}, {"label": "batch_size", "name": "batch_size", "type": "INT", "widget": {"name": "batch_size"}, "link": 418}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "shape": 6, "type": "IMAGE", "links": [417]}], "properties": {"Node name for S&R": "RebatchImages", "cnr_id": "comfy-core", "ver": "0.3.40", "widget_ue_connectable": {}}, "widgets_values": [1]}, {"id": 267, "type": "JWInteger", "pos": [-570.360107421875, 691.9722900390625], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [418]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [3]}, {"id": 6, "type": "CLIPTextEncode", "pos": [-80, 60], "size": [420, 280], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 392}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [96]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.34", "hasSecondTab": false, "widget_ue_connectable": {}, "secondTabText": "Send Back", "enableTabs": false, "secondTabOffset": 80, "tabWidth": 65, "secondTabWidth": 65, "tabXOffset": 10}, "widgets_values": ["卡通女孩骑着黑马"], "color": "#232", "bgcolor": "#353"}, {"id": 256, "type": "LoadImage", "pos": [-1652.6300048828125, 22.05068588256836], "size": [477.2256164550781, 529.4220581054688], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [408, 415, 422]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": ["583e3426417bbbb622db4bf122c4e81c90d27f44f4e69f716b3c76e50d7d4fc9.jpeg", "image", ""], "color": "#223", "bgcolor": "#335"}, {"id": 265, "type": "ImageMirror", "pos": [-945.79541015625, 404.9502258300781], "size": [315, 58], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 415}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [423]}], "properties": {"Node name for S&R": "ImageMirror", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#353", "groupcolor": "#8A8", "color": "#232"}}, "widgets_values": ["horizontal"], "color": "#232", "bgcolor": "#353"}, {"id": 272, "type": "ImpactSwitch", "pos": [-1004.0581665039062, 808.2664184570312], "size": [315, 122], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "input1", "name": "input1", "shape": 7, "type": "IMAGE", "link": 422}, {"label": "input2", "name": "input2", "type": "IMAGE", "link": 423}, {"label": "input3", "name": "input3", "type": "IMAGE"}, {"label": "select", "name": "select", "type": "INT", "widget": {"name": "select"}, "link": 424}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [425]}, {"label": "selected_label", "name": "selected_label", "type": "STRING"}, {"label": "selected_index", "name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch", "widget_ue_connectable": {}}, "widgets_values": [1, false]}, {"id": 258, "type": "JWInteger", "pos": [-2499.0263671875, 1471.2177734375], "size": [315, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [403]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [720]}, {"id": 268, "type": "JWInteger", "pos": [-2517.72607421875, 1777.414794921875], "size": [315, 58], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [424]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [2]}, {"id": 248, "type": "LayerMask: ObjectDetectorFL2", "pos": [436.2889099121094, 1568.930419921875], "size": [378, 150], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 378}, {"label": "florence2_model", "name": "florence2_model", "type": "FLORENCE2", "link": 379}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 426}], "outputs": [{"label": "bboxes", "name": "bboxes", "type": "BBOXES", "links": [381, 385]}, {"label": "preview", "name": "preview", "type": "IMAGE"}], "properties": {"Node name for S&R": "LayerMask: ObjectDetectorFL2", "widget_ue_connectable": {}}, "widgets_values": ["horse", "left_to_right", "all", "0,"], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 274, "type": "CR Prompt Text", "pos": [-2498.703857421875, 1944.3707275390625], "size": [400, 200], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "prompt", "name": "prompt", "type": "STRING", "links": [426]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Prompt Text", "widget_ue_connectable": {}}, "widgets_values": ["horse"]}, {"id": 275, "type": "Note", "pos": [-2822.273681640625, 1975.9259033203125], "size": [210, 88], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["蒙版分割提示词"], "color": "#432", "bgcolor": "#653"}, {"id": 247, "type": "LayerMask: LoadFlorence2Model", "pos": [-170.1090850830078, 1683.2325439453125], "size": [504, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "florence2_model", "name": "florence2_model", "type": "FLORENCE2", "links": [379]}], "properties": {"Node name for S&R": "LayerMask: LoadFlorence2Model", "widget_ue_connectable": {}}, "widgets_values": ["Florence-2-Flux-Large"], "color": "rgba(27, 80, 119, 0.7)"}, {"id": 276, "type": "Note", "pos": [-2770.9287109375, 1805.2633056640625], "size": [210, 88], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["是否镜像参考图\n1不镜像2镜像"], "color": "#432", "bgcolor": "#653"}, {"id": 277, "type": "Note", "pos": [-2779.384521484375, 1461.18115234375], "size": [210, 88], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["最长边"], "color": "#432", "bgcolor": "#653"}, {"id": 278, "type": "Note", "pos": [-2757.1923828125, 1277.811279296875], "size": [210, 88], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["蒙版扩展"], "color": "#432", "bgcolor": "#653"}, {"id": 262, "type": "PDIMAGE_LongerSize", "pos": [2176.5693359375, -703.5283203125], "size": [315, 82], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 410}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [411]}], "properties": {"Node name for S&R": "PDIMAGE_LongerSize", "widget_ue_connectable": {}}, "widgets_values": [1024, "bicubic"]}, {"id": 263, "type": "VHS_VideoCombine", "pos": [3807.031982421875, -1224.8641357421875], "size": [537.25732421875, 358], "flags": {}, "order": 53, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 414}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "T8", "format": "video/h264-mp4.json", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "T8_00007_pzneu_1753767886.mp4", "workflow": "T8_00007.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/T8_00007.mp4", "format": "video/h264-mp4.json", "subfolder": "", "type": "output", "frame_rate": 16}}}}, {"id": 260, "type": "ImageConcanate", "pos": [1585.4814453125, -701.2526245117188], "size": [315, 102], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "image1", "name": "image1", "type": "IMAGE", "link": 407}, {"label": "image2", "name": "image2", "type": "IMAGE", "link": 408}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [410]}], "properties": {"Node name for S&R": "ImageConcanate", "widget_ue_connectable": {}}, "widgets_values": ["down", true]}, {"id": 254, "type": "CLIPLoader", "pos": [-881.1427612304688, -313.6300048828125], "size": [350, 106], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [392, 393]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.34", "models": [{"name": "umt5_xxl_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp16.safetensors", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp16.safetensors", "wan", "default"], "color": "#322", "bgcolor": "#533"}], "links": [[96, 6, 0, 49, 0, "CONDITIONING"], [97, 7, 0, 49, 1, "CONDITIONING"], [333, 213, 0, 208, 0, "IMAGE"], [334, 216, 0, 208, 1, "IMAGE"], [340, 211, 2, 213, 1, "INT"], [341, 208, 0, 214, 0, "IMAGE"], [344, 208, 0, 49, 3, "IMAGE"], [352, 219, 0, 216, 0, "MASK"], [355, 233, 0, 229, 0, "MODEL"], [356, 230, 0, 231, 0, "MODEL"], [357, 231, 0, 232, 0, "MODEL"], [358, 234, 0, 233, 0, "MODEL"], [359, 229, 0, 235, 0, "MODEL"], [360, 232, 0, 236, 0, "MODEL"], [362, 235, 0, 239, 0, "MODEL"], [363, 240, 0, 239, 3, "LATENT"], [364, 237, 0, 239, 4, "INT"], [365, 236, 0, 240, 0, "MODEL"], [366, 237, 0, 240, 4, "INT"], [367, 239, 0, 241, 0, "LATENT"], [368, 241, 0, 242, 0, "LATENT"], [369, 49, 0, 240, 1, "CONDITIONING"], [370, 49, 1, 240, 2, "CONDITIONING"], [371, 49, 2, 240, 3, "LATENT"], [372, 49, 0, 239, 1, "CONDITIONING"], [373, 49, 1, 239, 2, "CONDITIONING"], [376, 244, 0, 211, 0, "IMAGE"], [377, 244, 0, 213, 0, "IMAGE"], [378, 246, 0, 248, 0, "IMAGE"], [379, 247, 0, 248, 1, "FLORENCE2"], [380, 246, 0, 249, 0, "IMAGE"], [381, 248, 0, 249, 1, "BBOXES"], [382, 249, 0, 250, 2, "MASK"], [383, 244, 0, 246, 0, "IMAGE"], [384, 244, 0, 250, 0, "IMAGE"], [385, 248, 0, 250, 1, "BBOXES"], [386, 251, 0, 252, 1, "INT"], [387, 250, 0, 252, 0, "MASK"], [388, 252, 0, 49, 4, "MASK"], [389, 252, 0, 208, 2, "MASK"], [390, 252, 0, 219, 0, "MASK"], [392, 254, 0, 6, 0, "CLIP"], [393, 254, 0, 7, 0, "CLIP"], [394, 253, 0, 49, 2, "VAE"], [395, 253, 0, 242, 1, "VAE"], [397, 211, 0, 49, 6, "INT"], [398, 211, 1, 49, 8, "INT"], [399, 211, 2, 49, 7, "INT"], [401, 257, 0, 244, 0, "IMAGE"], [402, 255, 0, 257, 2, "INT"], [403, 258, 0, 244, 2, "INT"], [404, 49, 3, 241, 1, "INT"], [405, 242, 0, 259, 0, "IMAGE"], [406, 259, 0, 238, 0, "IMAGE"], [407, 257, 0, 260, 0, "IMAGE"], [408, 256, 0, 260, 1, "IMAGE"], [410, 260, 0, 262, 0, "IMAGE"], [411, 262, 0, 261, 0, "IMAGE"], [412, 259, 0, 261, 1, "IMAGE"], [413, 261, 0, 264, 0, "IMAGE"], [414, 264, 0, 263, 0, "IMAGE"], [415, 256, 0, 265, 0, "IMAGE"], [417, 266, 0, 49, 5, "IMAGE"], [418, 267, 0, 266, 1, "INT"], [422, 256, 0, 272, 0, "*"], [423, 265, 0, 272, 1, "IMAGE"], [424, 268, 0, 272, 3, "INT"], [425, 272, 0, 266, 0, "IMAGE"], [426, 274, 0, 248, 2, "STRING"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.45, "offset": [2981.6983167860244, 1264.1855604383682]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "node_versions": {"comfy-core": "0.3.34"}}, "version": 0.4}