{"id": "a134cd7c-6987-4853-811d-7d76b530778e", "revision": 0, "last_node_id": 26, "last_link_id": 33, "nodes": [{"id": 1, "type": "CLIPTextEncode", "pos": [1570.701171875, 1166.4056396484375], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 1}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [14]}], "title": "CLIP Text Encode (Negative Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#322", "bgcolor": "#533"}, {"id": 5, "type": "CLIPLoader", "pos": [1187.701171875, 967.4055786132812], "size": [360, 106], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [1, 12]}], "properties": {"Node name for S&R": "CLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 8, "type": "VAELoader", "pos": [1187.701171875, 1117.4056396484375], "size": [360, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [15, 18]}], "properties": {"Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 10, "type": "Note", "pos": [957.7012939453125, 1117.4056396484375], "size": [210, 159.49227905273438], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["This model uses the wan 2.1 VAE.\n\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 13, "type": "LoadImage", "pos": [1107.701171875, 1327.4056396484375], "size": [450, 540], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [16]}, {"label": "MASK", "name": "MASK", "type": "MASK", "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage", "widget_ue_connectable": {}}, "widgets_values": ["c5f8f3e3e75e37761bcedc3a9600ced7fd08ea4fe4588daf2c806ba4f33e02fd.png", "image", ""]}, {"id": 14, "type": "WanImageToVideo", "pos": [1649.4373779296875, 1395.2037353515625], "size": [342.5999755859375, 210], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 13}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 14}, {"label": "vae", "name": "vae", "type": "VAE", "link": 15}, {"label": "clip_vision_output", "name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT"}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 16}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 31}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 32}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 33}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [5, 9]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [6, 10]}, {"label": "latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [11]}], "properties": {"Node name for S&R": "WanImageToVideo", "widget_ue_connectable": {}}, "widgets_values": [704, 544, 81, 1]}, {"id": 15, "type": "VAEDecode", "pos": [2747.701904296875, 757.4056396484375], "size": [210, 46], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 17}, {"label": "vae", "name": "vae", "type": "VAE", "link": 18}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [19]}], "properties": {"Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 3, "type": "ModelSamplingSD3", "pos": [1641.7032470703125, 831.8677368164062], "size": [315, 58], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 29}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [22]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [8]}, {"id": 18, "type": "TeaCache", "pos": [2433.353271484375, 524.6143798828125], "size": [315, 154], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 22}], "outputs": [{"label": "model", "name": "model", "type": "MODEL", "links": [23]}], "properties": {"Node name for S&R": "TeaCache", "widget_ue_connectable": {}}, "widgets_values": ["wan2.1_i2v_720p_14B", 0.26000000000000006, 0, 1, "cuda"]}, {"id": 19, "type": "LayerUtility: PurgeVRAM", "pos": [2354.7177734375, 1185.4923095703125], "size": [315, 82], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 24}], "outputs": [], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 21, "type": "LayerUtility: PurgeVRAM", "pos": [2738.505615234375, 1188.4722900390625], "size": [315, 82], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 25}], "outputs": [], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 4, "type": "KSamplerAdvanced", "pos": [2420.211669921875, 750.6731567382812], "size": [304.748046875, 334], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 23}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 5}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 6}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 7}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [17, 25]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 0, "fixed", 20, 3.5, "euler", "simple", 10, 10000, "disable"]}, {"id": 12, "type": "CLIPTextEncode", "pos": [1572.701171875, 963.4055786132812], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 12}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [13]}], "title": "CLIP Text Encode (Positive Prompt)", "properties": {"Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["A girl crouched down and picked up a coin from the ground"], "color": "#232", "bgcolor": "#353"}, {"id": 9, "type": "Note", "pos": [527.6240234375, 644.4146728515625], "size": [210, 159.49227905273438], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["This model uses a different diffusion model for the first steps (high noise) vs the last steps (low noise).\n\n"], "color": "#432", "bgcolor": "#653"}, {"id": 16, "type": "VHS_VideoCombine", "pos": [3108.99267578125, 765.3596801757812], "size": [893.4776000976562, 358], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 19}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4.json", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "AnimateDiff_00001_tteun_1753721675.mp4", "workflow": "AnimateDiff_00001.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/AnimateDiff_00001.mp4", "format": "video/h264-mp4.json", "subfolder": "", "type": "output", "frame_rate": 16}}}}, {"id": 23, "type": "PathchSageAttentionKJ", "pos": [1275.124755859375, 778.37939453125], "size": [315, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 28}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [29]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 7, "type": "UNETLoader", "pos": [803.8261108398438, 838.43408203125], "size": [430, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [28]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_i2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 11, "type": "KSamplerAdvanced", "pos": [2050.70751953125, 747.482177734375], "size": [304.748046875, 334], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 21}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 9}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 10}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 11}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [7, 24]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["enable", 545307403001396, "randomize", 20, 3.5, "euler", "simple", 0, 10, "enable"]}, {"id": 17, "type": "TeaCache", "pos": [1932.70703125, 516.5867309570312], "size": [315, 154], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 20}], "outputs": [{"label": "model", "name": "model", "type": "MODEL", "links": [21]}], "properties": {"Node name for S&R": "TeaCache", "widget_ue_connectable": {}}, "widgets_values": ["wan2.1_i2v_720p_14B", 0.26000000000000006, 0, 1, "cuda"]}, {"id": 24, "type": "JWInteger", "pos": [1212.5025634765625, 2022.0087890625], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [31]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [704]}, {"id": 25, "type": "JWInteger", "pos": [1219.9471435546875, 2188.34228515625], "size": [315, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [32]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [544]}, {"id": 26, "type": "JWInteger", "pos": [1221.280517578125, 2333.897705078125], "size": [315, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [33]}], "properties": {"Node name for S&R": "JWInteger", "widget_ue_connectable": {}}, "widgets_values": [81]}, {"id": 6, "type": "UNETLoader", "pos": [696.4904174804688, 528.1093139648438], "size": [430, 82], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [26]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_i2v_high_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 22, "type": "PathchSageAttentionKJ", "pos": [1181.8504638671875, 533.8287963867188], "size": [315, 58], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 26}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [27]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"]}, {"id": 2, "type": "ModelSamplingSD3", "pos": [1570.3365478515625, 531.0609130859375], "size": [315, 58], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 27}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [20]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002]}], "links": [[1, 5, 0, 1, 0, "CLIP"], [5, 14, 0, 4, 1, "CONDITIONING"], [6, 14, 1, 4, 2, "CONDITIONING"], [7, 11, 0, 4, 3, "LATENT"], [9, 14, 0, 11, 1, "CONDITIONING"], [10, 14, 1, 11, 2, "CONDITIONING"], [11, 14, 2, 11, 3, "LATENT"], [12, 5, 0, 12, 0, "CLIP"], [13, 12, 0, 14, 0, "CONDITIONING"], [14, 1, 0, 14, 1, "CONDITIONING"], [15, 8, 0, 14, 2, "VAE"], [16, 13, 0, 14, 4, "IMAGE"], [17, 4, 0, 15, 0, "LATENT"], [18, 8, 0, 15, 1, "VAE"], [19, 15, 0, 16, 0, "IMAGE"], [20, 2, 0, 17, 0, "MODEL"], [21, 17, 0, 11, 0, "MODEL"], [22, 3, 0, 18, 0, "MODEL"], [23, 18, 0, 4, 0, "MODEL"], [24, 11, 0, 19, 0, "*"], [25, 4, 0, 21, 0, "*"], [26, 6, 0, 22, 0, "MODEL"], [27, 22, 0, 2, 0, "MODEL"], [28, 7, 0, 23, 0, "MODEL"], [29, 23, 0, 3, 0, "MODEL"], [31, 24, 0, 14, 5, "INT"], [32, 25, 0, 14, 6, "INT"], [33, 26, 0, 14, 7, "INT"]], "groups": [{"id": 1, "title": "B站、Youtube：T8star-Aix", "bounding": [801.2587890625, 140.72511291503906, 4168, 309], "color": "#3f789e", "font_size": 240, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.724729500000001, "offset": [-28.472200276833064, -273.3774384127255]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false}, "version": 0.4}