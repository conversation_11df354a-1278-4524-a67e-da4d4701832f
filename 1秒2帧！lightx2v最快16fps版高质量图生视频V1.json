{"id": "ffbeff3e-3fcf-4405-89e1-a99279a53ccb", "revision": 0, "last_node_id": 26, "last_link_id": 22, "nodes": [{"id": 4, "type": "CLIPVisionLoader", "pos": [1057.0848388671875, 1238.951904296875], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP_VISION", "name": "CLIP_VISION", "type": "CLIP_VISION", "links": [1]}], "properties": {"Node name for S&R": "CLIPVisionLoader", "cnr_id": "comfy-core", "ver": "0.3.26", "widget_ue_connectable": {}}, "widgets_values": ["clip_vision_h.safetensors"], "color": "#233", "bgcolor": "#355"}, {"id": 5, "type": "WanVideoClipVisionEncode", "pos": [1047.044921875, 1369.741455078125], "size": [327.5999755859375, 262], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "clip_vision", "name": "clip_vision", "type": "CLIP_VISION", "link": 1}, {"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 2}, {"label": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE"}, {"label": "negative_image", "name": "negative_image", "shape": 7, "type": "IMAGE"}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_CLIPEMBEDS", "links": [6]}], "properties": {"Node name for S&R": "WanVideoClipVisionEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [1, 1, "center", "average", true, 0, 0.20000000000000004], "color": "#233", "bgcolor": "#355"}, {"id": 7, "type": "WanVideoLoraSelect", "pos": [1064.5465087890625, 941.8665161132812], "size": [386.9990539550781, 155.43142700195312], "flags": {}, "order": 1, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [4]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "c3ee35f3ece76e38099dc516182d69b406e16772", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#533", "groupcolor": "#A88", "color": "#322"}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 1.0000000000000002, false], "color": "#322", "bgcolor": "#533"}, {"id": 9, "type": "LoadWanVideoT5TextEncoder", "pos": [2160.080810546875, 768.7777099609375], "size": [369.7645568847656, 133.63636779785156], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [12]}], "properties": {"Node name for S&R": "LoadWanVideoT5TextEncoder", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}}, "widgets_values": ["umt5-xxl-enc-fp8_e4m3fn.safetensors", "bf16", "offload_device", "fp8_e4m3fn"], "color": "#322", "bgcolor": "#533"}, {"id": 11, "type": "LoadImage", "pos": [733.28125, 1781.5411376953125], "size": [413.10479736328125, 498.3180847167969], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [2, 19]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.26", "widget_ue_connectable": {}}, "widgets_values": ["87357d529c798bd10946b9adacad99a7bc5aeb1ea76c9b6098dabcc213f2427b.png", "image", ""], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 13, "type": "WanVideoVRAMManagement", "pos": [1194.5718994140625, 741.8529052734375], "size": [315, 58], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "vram_management_args", "name": "vram_management_args", "type": "VRAM_MANAGEMENTARGS", "links": []}], "properties": {"Node name for S&R": "WanVideoVRAMManagement", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [1], "color": "#223", "bgcolor": "#335"}, {"id": 14, "type": "WanVideoVAELoader", "pos": [1640.0657958984375, 875.0149536132812], "size": [372.7727966308594, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [5, 10]}], "properties": {"Node name for S&R": "WanVideoVAELoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 15, "type": "JWInteger", "pos": [1476.8594970703125, 2042.91943359375], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [20]}], "properties": {"Node name for S&R": "JWInteger", "ttNbgOverride": {"bgcolor": "#533", "groupcolor": "#A88", "color": "#322"}}, "widgets_values": [832], "color": "#322", "bgcolor": "#533"}, {"id": 21, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [1490.125244140625, 1613.33154296875], "size": [336, 330], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 19}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 20}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [7]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT", "links": [8]}, {"label": "height", "name": "height", "type": "INT", "links": [9]}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "16", "longest", 832, "#000000"], "color": "#322", "bgcolor": "#533"}, {"id": 12, "type": "WanVideoModelLoader", "pos": [1582.6240234375, 542.4228515625], "size": [477.4410095214844, 254], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 3}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 4}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH"}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [14]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_1-I2V-14B-480P_fp8_e4m3fn.safetensors", "bf16", "fp8_e4m3fn", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 17, "type": "WanVideoDecode", "pos": [3203.88427734375, 584.9080200195312], "size": [315, 174], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 10}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 11}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "slot_index": 0, "links": [18]}], "properties": {"Node name for S&R": "WanVideoDecode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [false, 272, 272, 144, 128], "color": "#322", "bgcolor": "#533"}, {"id": 6, "type": "WanVideoEnhanceAVideo", "pos": [2683.33056640625, 739.6854248046875], "size": [315, 106], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "feta_args", "name": "feta_args", "type": "FETAARGS", "links": [17]}], "properties": {"Node name for S&R": "WanVideoEnhanceAVideo", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#533", "groupcolor": "#A88", "color": "#322"}}, "widgets_values": [2, 0, 1], "color": "#322", "bgcolor": "#533"}, {"id": 24, "type": "Note", "pos": [465.5169677734375, 1582.43408203125], "size": [210, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["总帧数"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "Text Multiline", "pos": [685.6746826171875, 1359.1865234375], "size": [279.8829345703125, 88], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [13]}], "properties": {"Node name for S&R": "Text Multiline"}, "widgets_values": ["女人转过身体，正对镜头，抬起手，打招呼，微笑"], "color": "#322", "bgcolor": "#533"}, {"id": 25, "type": "Note", "pos": [457.2068176269531, 1369.6339111328125], "size": [210, 88], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["提示词"], "color": "#432", "bgcolor": "#653"}, {"id": 8, "type": "WanVideoBlockSwap", "pos": [1176.4510498046875, 526.19140625], "size": [315, 154], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "slot_index": 0, "links": [3]}], "properties": {"Node name for S&R": "WanVideoBlockSwap", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [30, false, false, true, 0], "color": "#223", "bgcolor": "#335"}, {"id": 18, "type": "WanVideoTextEncode", "pos": [2792.230224609375, 1606.5518798828125], "size": [325.2562255859375, 196], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "t5", "name": "t5", "type": "WANTEXTENCODER", "link": 12}, {"label": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL"}, {"label": "positive_prompt", "name": "positive_prompt", "type": "STRING", "widget": {"name": "positive_prompt"}, "link": 13}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [15]}], "properties": {"Node name for S&R": "WanVideoTextEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}}, "widgets_values": ["", "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", false], "color": "#322", "bgcolor": "#533"}, {"id": 20, "type": "VHS_VideoCombine", "pos": [3643.165771484375, 522.4730224609375], "size": [1025.05126953125, 1688.068359375], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 18}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#533", "groupcolor": "#A88", "color": "#322"}}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_I2V_00003_cegdl_1750196554.mp4", "workflow": "WanVideoWrapper_I2V_00003.png", "fullpath": "/data/ComfyUI/personal/97e72773b3062cc68e83823cfbb22717/output/WanVideoWrapper_I2V_00003.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 24}}}, "color": "#322", "bgcolor": "#533"}, {"id": 16, "type": "WanVideoImageToVideoEncode", "pos": [2181.449462890625, 1213.3045654296875], "size": [352.79998779296875, 390], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 5}, {"label": "clip_embeds", "name": "clip_embeds", "shape": 7, "type": "WANVIDIMAGE_CLIPEMBEDS", "link": 6}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 7}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE"}, {"label": "control_embeds", "name": "control_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "temporal_mask", "name": "temporal_mask", "shape": 7, "type": "MASK"}, {"label": "extra_latents", "name": "extra_latents", "shape": 7, "type": "LATENT"}, {"label": "realisdance_latents", "name": "realisdance_latents", "shape": 7, "type": "REALISDANCELATENTS"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 8}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 9}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 22}], "outputs": [{"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [16]}], "properties": {"Node name for S&R": "WanVideoImageToVideoEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {"width": true, "height": true}}, "widgets_values": [832, 480, 81, 0.030000000000000006, 1, 1, true, false, false], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 23, "type": "JWInteger", "pos": [712.5540161132812, 1576.2303466796875], "size": [315, 58], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [22]}], "properties": {"Node name for S&R": "JWInteger", "ttNbgOverride": {"bgcolor": "#533", "groupcolor": "#A88", "color": "#322"}}, "widgets_values": [121], "color": "#322", "bgcolor": "#533"}, {"id": 19, "type": "WanVideoSampler", "pos": [3164.42138671875, 932.7518920898438], "size": [339.36614990234375, 588.0098266601562], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 14}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 15}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 16}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS", "link": 17}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS"}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS"}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS"}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "slot_index": 0, "links": [11]}], "properties": {"Node name for S&R": "WanVideoSampler", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#533", "groupcolor": "#A88", "color": "#322"}}, "widgets_values": [5, 1.0000000000000002, 8.000000000000002, 993487226447028, "randomize", false, "lcm", 0, 1, "", "comfy"], "color": "#322", "bgcolor": "#533"}], "links": [[1, 4, 0, 5, 0, "CLIP_VISION"], [2, 11, 0, 5, 1, "IMAGE"], [3, 8, 0, 12, 1, "BLOCKSWAPARGS"], [4, 7, 0, 12, 2, "WANVIDLORA"], [5, 14, 0, 16, 0, "WANVAE"], [6, 5, 0, 16, 1, "WANVIDIMAGE_CLIPEMBEDS"], [7, 21, 0, 16, 2, "IMAGE"], [8, 21, 3, 16, 8, "INT"], [9, 21, 4, 16, 9, "INT"], [10, 14, 0, 17, 0, "WANVAE"], [11, 19, 0, 17, 1, "LATENT"], [12, 9, 0, 18, 0, "WANTEXTENCODER"], [13, 10, 0, 18, 2, "STRING"], [14, 12, 0, 19, 0, "WANVIDEOMODEL"], [15, 18, 0, 19, 1, "WANVIDEOTEXTEMBEDS"], [16, 16, 0, 19, 2, "WANVIDIMAGE_EMBEDS"], [17, 6, 0, 19, 4, "FETAARGS"], [18, 17, 0, 20, 0, "IMAGE"], [19, 11, 0, 21, 0, "IMAGE"], [20, 15, 0, 21, 2, "INT"], [22, 23, 0, 16, 10, "INT"]], "groups": [{"id": 1, "title": "B站、Youtube：T8star-Aix", "bounding": [1113.4539794921875, 137.18272399902344, 4168, 309], "color": "#3f789e", "font_size": 240, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.8769226950000037, "offset": [-2144.3372653142947, -243.23405813073217]}, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}